# DAN Social - Merchant Management Platform

## Overview

DAN Social is a web-based merchant management platform built with React, Vite, and Tailwind CSS. The platform provides tools for merchants to manage their accounts, view analytics, and interact with customers.

## Features

- User authentication and account management
- Dashboard with analytics and reporting
- Responsive design for desktop and mobile devices
- Modern UI with Ant Design components
- Data visualization with ECharts

## Tech Stack

- **Frontend Framework**: React 18
- **Build Tool**: Vite
- **CSS Framework**: Tailwind CSS
- **UI Components**: Ant Design
- **Charts**: ECharts
- **Routing**: React Router DOM
- **HTTP Client**: Axios

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository

   ```bash
   git clone https://github.com/rfstllc-org/dan-social-web.git
   cd dan-social-web
   ```

2. Install dependencies

   ```bash
   pnpm install
   ```

3. Start the development server
   ```bash
   pnpm dev
   ```

### Building for Production

```bash
pnpm run build
```

The build artifacts will be stored in the `dist/` directory.

## Tailwind CSS Setup

This project uses Tailwind CSS for styling. The build process compiles the Tailwind directives into a final CSS file.

To manually rebuild the CSS:

```bash
npm run build:css
```

To watch for CSS changes during development:

```bash
npm run watch:css
```

## Available Scripts

- `npm run dev`: Start the development server
- `npm run build`: Build for production
- `npm run preview`: Preview the production build
- `npm run lint`: Run ESLint
- `npm run format`: Format code with Prettier
- `npm run build:css`: Build Tailwind CSS
- `npm run watch:css`: Watch and build Tailwind CSS

## Troubleshooting

### Tailwind CSS not working

If Tailwind styles are not being applied:

1. Make sure the CSS is being built correctly:

   ```bash
   npm run build:css
   ```

2. Check that `tailwind.css` is being imported in `main.jsx`

3. Verify that your `tailwind.config.js` has the correct content paths:

   ```js
   content: ['./index.html', './src/**/*.{js,jsx,ts,tsx}'];
   ```

4. Try running the Tailwind CLI directly:

   ```bash
   npx tailwindcss -i ./src/index.css -o ./src/tailwind.css
   ```

5. Make sure you're running the dev server after building the CSS:
   ```bash
   npm run build:css && npm run dev
   ```

## Git Configuration

To set up the correct Git user for this project:

```bash
git config user.name "yuli-focused"
git config user.email "<EMAIL>"
```

## License

Copyright © 2025 DAN Social
