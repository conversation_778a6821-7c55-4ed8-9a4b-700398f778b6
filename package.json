{"name": "dan-social-web", "version": "1.0.0", "description": "DAN Social - Merchant Management Platform", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "concurrently \"npm run watch:css\" \"vite\"", "sit": "concurrently \"npm run watch:css\" \"vite --mode sit\"", "build": "npm run build:css && tsc && vite build", "build:sit": "vite build --mode sit", "preview": "vite preview --port 8080", "lint": "eslint src --ext js,jsx,ts,tsx --report-unused-disable-directives", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css}\"", "build:css": "tailwindcss -i ./src/index.css -o ./src/tailwind.css", "watch:css": "tailwindcss -i ./src/index.css -o ./src/tailwind.css --watch", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.5", "axios": "^1.6.2", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "echarts-liquidfill": "^3.1.0", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.1.3", "react-router-dom": "^6.21.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^22.15.21", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^9.1.2", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.8.3", "vite": "^5.0.10", "vite-plugin-babel": "^1.3.1", "vite-plugin-svgr": "^4.3.0"}, "keywords": ["react", "vite", "antd", "dashboard", "management"], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0"}