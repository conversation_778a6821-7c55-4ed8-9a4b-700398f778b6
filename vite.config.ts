import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 根据模式确定API目标地址
  const getApiTarget = () => {
    console.log(`🔧 Current mode: ${mode}`);

    switch (mode) {
      case 'sit':
        return 'http://test.dan-social.glor.cn';
      case 'production':
        return 'https://prod.dan-social.glor.cn'; // 生产环境地址
      case 'development':
      default:
        return 'http://dev.dan-social.glor.cn';
    }
  };

  const apiTarget = getApiTarget();
  console.log(`🚀 API Target: ${apiTarget}`);

  return {
    plugins: [
      react(),
      svgr({
        svgrOptions: {
          exportType: 'default',
        },
      })
    ],
    server: {
      host: '0.0.0.0', // 允许外部访问
      port: 5173, // 明确指定端口
      open: true, // 自动打开浏览器
      proxy: {
        '/api': {
          target: apiTarget,
          changeOrigin: true,
          secure: false, // 如果是https目标，但证书有问题时使用
          // 如果API路径不包含/api前缀，可以使用rewrite去掉
          // rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    resolve: {
      alias: {
        '@': '/src'
      }
    },
    define: {
      // 定义全局常量，可以在代码中使用
      __API_BASE_URL__: JSON.stringify(apiTarget)
    }
  };
});
