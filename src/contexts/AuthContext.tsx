import { createContext, useContext, useState, useEffect } from 'react';
import {
  login as apiLogin,
  logout as apiLogout,
  getUserContext,
  switchRole as apiSwitchRole,
  clearRole as apiClearRole,
  getMenus,
  updateUserPreference
} from '@/api';
import type { User, MenuItem, AuthContextType, AuthProviderProps, UserContextWithRoleIds, LanguageCode } from '@/types';
import { RoleCodeEnums, UserPreferenceUpdateType } from '@/types';
import { encryptData, storageUtils } from '@/utils/utils';
import { RememberMeManager } from '@/utils/rememberMe';

// 创建认证上下文
const AuthContext = createContext<AuthContextType | null>(null);

// 获取浏览器时区的辅助函数
const getBrowserTimeZone = (): string => {
  try {
    // 使用 Intl.DateTimeFormat().resolvedOptions().timeZone 获取浏览器时区
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.error('Failed to get browser timezone:', error);
    // 如果获取失败，返回 UTC 作为默认值
    return 'UTC';
  }
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userContext, setUserContext] = useState<UserContextWithRoleIds | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [menus, setMenus] = useState<MenuItem[]>([]);
  // 新增：全局存储merchantId和agentId
  const [merchantId, setMerchantId] = useState<number>();
  const [agentId, setAgentId] = useState<number>();
  // 临时language存储，用于login之前用户切换的语言可以在登录的时候设置
  const [temporaryLanguage, setTemporaryLanguage] = useState<LanguageCode>();
  // 获取用户上下文信息
  const fetchUserContext = async (): Promise<void> => {
    const contextData = await getUserContext();
    if (contextData) {
      const merchantRoleId = contextData.availableRoles.find(role => role.roleCode === RoleCodeEnums.MERCHANT)?.id;
      const agentRoleId = contextData.availableRoles.find(role => role.roleCode === RoleCodeEnums.AGENT)?.id;
      setUserContext({
        ...contextData,
        merchantRoleId: merchantRoleId as number,
        agentRoleId,
        agentId: contextData?.user?.agentId
      });
    }
  };

  // 添加获取菜单的方法
  const fetchMenus = async (): Promise<void> => {
    const menuData = await getMenus();
    setMenus(menuData);
  };

  // 更新用户时区偏好
  const updateUserTimeZone = async (userId: number): Promise<void> => {
    try {
      const browserTimeZone = getBrowserTimeZone();
      await updateUserPreference(userId, UserPreferenceUpdateType.TIME_ZONE, undefined, browserTimeZone);
    } catch (error) {
      console.error('Failed to update user timezone preference:', error);
      // 即使更新失败也不影响登录流程
    }
  };

  // 清除角色
  const clearRole = async (): Promise<void> => {
    const success = await apiClearRole();
    if (success) {
      // 清除角色成功后重新获取用户上下文信息
      await fetchUserContext();
      // 回到默认AGENT权限，重新获取Agent菜单
      await fetchMenus();
      setMerchantId(undefined);
      storageUtils.removeItem('merchantId');
    }
  };

  const switchRole = async (roleId?: number, merchantId?: number): Promise<boolean> => {
    if (!roleId) return false;
    const success = await apiSwitchRole(roleId, merchantId as number);
    if (success) {
      if (merchantId) {
        setMerchantId(merchantId);
        storageUtils.setItem('merchantId', merchantId.toString());
      }
      await fetchMenus();
    }
    return success;
  };

  const syncLanguageToServer = async (userId: number, languageCode: LanguageCode | undefined) => {
    if (userId && languageCode) {
      await updateUserPreference(userId, UserPreferenceUpdateType.LANGUAGE, languageCode);
      setTemporaryLanguage(undefined);
    }
  };

  // 自动登录函数（只在 AuthProvider 初始化时用）
  const autoLogin = async (): Promise<boolean> => {
    const rememberMeData = RememberMeManager.get();
    if (!rememberMeData) return false;

    try {
      // 直接调用API登录，避免重复调用
      const userData = await apiLogin(rememberMeData.username, rememberMeData.password);

      if (userData) {
        const { token, userId, username, agentId, merchantId } = userData;
        const userDataWithoutToken: Omit<User, 'token'> = {
          userId,
          username
        };

        setMerchantId(merchantId);
        setAgentId(agentId);
        setCurrentUser(userData);

        // 自动登录时使用持久存储（因为有Remember Me数据）
        storageUtils.setItem('user', JSON.stringify(userDataWithoutToken), true);
        storageUtils.setItem('token', encryptData(token), true);
        storageUtils.setItem('agentId', agentId?.toString() || '', true);
        storageUtils.setItem('merchantId', merchantId?.toString() || '', true);

        // 更新记住登录信息中的token和时间戳
        RememberMeManager.save(token, rememberMeData.username, rememberMeData.password);

        // 登录成功后获取用户上下文信息
        await fetchUserContext();

        // 登录成功后获取菜单
        await fetchMenus();

        // 登录成功后更新用户时区偏好
        await updateUserTimeZone(userId);

        syncLanguageToServer(userId, temporaryLanguage);

        return true;
      }
      return false;
    } catch (error) {
      console.error('Auto login failed:', error);
      RememberMeManager.clear();
      return false;
    }
  };

  // 登录函数
  const login = async (account: string, password: string, rememberMe: boolean = false): Promise<boolean> => {
    const userData = await apiLogin(account, password);

    if (userData) {
      const { token, userId, username, agentId, merchantId } = userData;
      const userDataWithoutToken: Omit<User, 'token'> = {
        userId,
        username
      };
      setMerchantId(merchantId);
      setAgentId(agentId);
      setCurrentUser(userData);

      // 根据是否勾选Remember Me决定存储方式
      // rememberMe=true: 使用localStorage (持久存储)
      // rememberMe=false: 使用sessionStorage (会话存储)
      storageUtils.setItem('user', JSON.stringify(userDataWithoutToken), rememberMe);
      storageUtils.setItem('token', encryptData(token), rememberMe);
      storageUtils.setItem('agentId', agentId?.toString() || '', rememberMe);
      storageUtils.setItem('merchantId', merchantId?.toString() || '', rememberMe);

      // 如果勾选了记住登录，保存Remember Me信息
      if (rememberMe) {
        RememberMeManager.save(token, account, password);
      }

      // 登录成功后获取用户上下文信息
      await fetchUserContext();

      // 登录成功后获取菜单
      await fetchMenus();

      // 登录成功后更新用户时区偏好
      await updateUserTimeZone(userId);

      syncLanguageToServer(userId, temporaryLanguage);

      return true;
    }
    return false;
  };

  const clearAuth = () => {
    // 清理当前用户的语言设置
    if (currentUser?.userId) {
      const languageStorageKey = `userLanguage_${currentUser.userId}`;
      storageUtils.removeItem(languageStorageKey);
    }
    setCurrentUser(null);
    setUserContext(null);
    setMenus([]);
    setMerchantId(undefined);
    setAgentId(undefined);
    storageUtils.removeItem('user');
    storageUtils.removeItem('token');
    storageUtils.removeItem('merchantId');
    storageUtils.removeItem('agentId');
    // 注意：不删除访客语言设置，保留未登录用户的语言偏好
  };

  // 修改登出函数，清除记住登录信息
  const logout = async (): Promise<void> => {
    // 清除记住登录信息
    RememberMeManager.clear();

    // 先清除本地状态，避免新的请求
    clearAuth();

    // 延迟调用登出接口，确保当前请求完成
    setTimeout(async () => {
      try {
        await apiLogout();
      } catch (error) {
        console.error('Logout API call failed:', error);
      }
    }, 100);
  };

  // 只在全局初始化时自动登录一次
  useEffect(() => {
    const initAuth = async () => {
      // 只有在有有效的Remember Me数据时才自动登录
      // 这样确保只有勾选了"记住我"的用户才会保持登录状态
      if (RememberMeManager.hasValidData()) {
        await autoLogin();
      }
      setLoading(false);
    };
    initAuth();
  }, []);

  const value: AuthContextType = {
    currentUser,
    userContext,
    login,
    logout,
    loading,
    menus,
    switchRole,
    clearRole,
    merchantId,
    agentId,
    clearAuth,
    temporaryLanguage,
    setTemporaryLanguage,
    autoLogin
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
