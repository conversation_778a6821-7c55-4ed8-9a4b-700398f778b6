import { createContext, useContext, useState, useEffect } from 'react';
import { Merchant } from '@/types/merchant';
import { storageUtils } from '@/utils/utils';

interface MerchantProviderProps {
  children: React.ReactNode;
}

interface MerchantContextType {
  merchant?: Merchant;
  setMerchant: (merchant: Merchant) => void;
  clearMerchant: () => void;
}

const MERCHANT_STORAGE_KEY = 'selected_merchant';

const MerchantContext = createContext<MerchantContextType | null>(null);

export const MerchantProvider = ({ children }: MerchantProviderProps) => {
  const [merchant, setMerchantState] = useState<Merchant>();

  // 从localStorage读取merchant数据
  useEffect(() => {
    const storedMerchant = storageUtils.getItem(MERCHANT_STORAGE_KEY);
    if (storedMerchant) {
      try {
        const parsedMerchant = JSON.parse(storedMerchant);
        setMerchantState(parsedMerchant);
      } catch (error) {
        console.error('Failed to parse stored merchant data:', error);
        storageUtils.removeItem(MERCHANT_STORAGE_KEY);
      }
    }
  }, []);

  // 设置merchant并保存到localStorage
  const setMerchant = (merchant: Merchant) => {
    setMerchantState(merchant);
    storageUtils.setItem(MERCHANT_STORAGE_KEY, JSON.stringify(merchant));
  };

  // 清除merchant数据
  const clearMerchant = () => {
    setMerchantState(undefined);
    storageUtils.removeItem(MERCHANT_STORAGE_KEY);
  };

  return (
    <MerchantContext.Provider
      value={{
        merchant,
        setMerchant,
        clearMerchant
      }}
    >
      {children}
    </MerchantContext.Provider>
  );
};

export function useMerchant(): MerchantContextType {
  const context = useContext(MerchantContext);
  if (!context) {
    throw new Error('useMerchant must be used within an MerchantProvider');
  }
  return context;
}
