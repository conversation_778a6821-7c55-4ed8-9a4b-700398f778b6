@tailwind base;
@tailwind components;
@tailwind utilities;

/* 定义字体类 */
.font-fang-yuan {
  font-family: 'AlimamaFangYuanTi', sans-serif;
}

body {
  margin: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', '<PERSON>xygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.login-page .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #f44321 !important;
  border-color: #f44321 !important;
}

.login-page .ant-checkbox:hover .ant-checkbox-inner,
.login-page .ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #f44321 !important;
}

.login-page .ant-checkbox-checked::after {
  border-color: #f44321 !important;
}

/* 半选中状态 */
.login-page .ant-checkbox-indeterminate .ant-checkbox-inner {
  background-color: #f44321 !important;
  border-color: #f44321 !important;
}

.ant-checkbox-indeterminate .ant-checkbox-inner::after {
  background-color: #fff !important;
}

/* 使用更强的选择器覆盖自动填充样式 */
.login-page .ant-form-item .ant-input:-webkit-autofill,
.login-page .ant-form-item .ant-input:-webkit-autofill:hover,
.login-page .ant-form-item .ant-input:-webkit-autofill:focus,
.login-page .ant-form-item .ant-input:-webkit-autofill:active,
.login-page .ant-form-item .ant-input:-internal-autofill-selected,
.login-page .ant-form-item input:-webkit-autofill,
.login-page .ant-form-item input:-webkit-autofill:hover,
.login-page .ant-form-item input:-webkit-autofill:focus,
.login-page .ant-form-item input:-webkit-autofill:active,
.login-page .ant-form-item input:-internal-autofill-selected {
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  -webkit-text-fill-color: #e0e0e0 !important;
  background-color: transparent !important;
  color: #e0e0e0 !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* 针对密码输入框的特殊处理 */
.login-page .ant-form-item .ant-input-affix-wrapper input:-webkit-autofill,
.login-page .ant-form-item .ant-input-affix-wrapper input:-webkit-autofill:hover,
.login-page .ant-form-item .ant-input-affix-wrapper input:-webkit-autofill:focus,
.login-page .ant-form-item .ant-input-affix-wrapper input:-webkit-autofill:active,
.login-page .ant-form-item .ant-input-affix-wrapper input:-internal-autofill-selected {
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  -webkit-text-fill-color: #e0e0e0 !important;
  background-color: transparent !important;
  color: #e0e0e0 !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.login-page .ant-input-outlined.ant-input-status-error:not(.ant-input-disabled) {
  background: transparent !important;
  border-color: #f44321 !important;
}

/* Custom menu styles */
.custom-menu {
  border-right: 0;
  padding-top: 10px;
  width: 280px;
  display: flex;
  flex-direction: column;
}

.custom-menu .ant-menu-item {
  color: #000;
  height: 48px;
  line-height: 48px; /* 64 - 8*2 padding */
  margin: 8px 16px;
  padding: 24px 20px;
  border-radius: 8px;
  transition: background 0.2s;
  box-sizing: border-box;
  width: calc(100% - 32px);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2px;
}

.custom-menu .menu-icon {
  filter: grayscale(1) opacity(0.25);
}

.custom-menu .ant-menu-item-selected {
  background-color: #b6354c;
}

.custom-menu .ant-menu-item-selected .menu-icon {
  filter: brightness(0) invert(1);
}

.custom-menu .ant-menu-item-selected a {
  color: #fff;
}

.custom-menu .ant-menu-item-selected {
  color: #fff;
}

.dan-social-web .ant-btn-primary {
  background-color: #0d728f;
  border-color: #0d728f;
  color: #fff;
}

.dan-social-web .ant-btn-primary:hover {
  background-color: #0d728f !important;
  border-color: #0d728f !important;
  color: #fff;
}

.dan-social-web .ant-btn-primary:active {
  background-color: #0d728f !important;
  border-color: #0d728f !important;
  color: #fff;
}

.dan-social-web .ant-btn-link {
  color: #0d728f;
}

.dan-social-web .ant-btn-link:hover {
  color: #0d728f !important;
}

.dan-social-web .ant-btn-link:active {
  color: #0d728f !important;
}
