// Warning API request parameters
export interface GetWarningListParams {
  agentId?: number;
  merchantId?: number;
  warningType?: string;
  page?: number;
  size?: number;
}

export interface WarningMessageBaseParams {
  warningId: number;
}

// Warning message simple DTO
export interface WarningMessageSimpleDTO {
  warningId: number;
  warningType: string;
  reviewCreateTime: string;
  analysisNotes: string;
  reviewComment: string;
  read: boolean;
  merchantName: string;
}

// Warning message detail DTO
export interface WarningMessageDetailDTO extends WarningMessageSimpleDTO {
  reviewerName: string;
  commentEditTime: string;
  replyContent: string;
  aiReply: boolean;
  warningTypes: string[];
  starRating: number;
  commentId: number;
}

// Paginated warning list response
export interface PageWarningMessageSimpleResponse {
  totalPages: number;
  totalElements: number;
  size: number;
  content: WarningMessageSimpleDTO[];
  number: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  pageable: {
    offset: number;
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    paged: boolean;
    pageNumber: number;
    pageSize: number;
    unpaged: boolean;
  };
  numberOfElements: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}
