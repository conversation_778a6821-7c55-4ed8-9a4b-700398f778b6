// API 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface ApiResponse<T> {
  data: T;
}

export enum CustomerType {
  AllCustomers = 'allCustomers',
  ReturningCustomers = 'returningCustomers'
}

export enum DimensionType {
  Food = 'Food',
  Service = 'Service',
  Delivery = 'Delivery',
  PriceValue = 'Price/Value',
  SafetyCleanliness = 'Safety & Cleanliness',
  Environment = 'Environment',
  DietaryInfo = 'Dietary Info'
}

// 维度分数
export interface DimensionScore {
  dimensionName: DimensionType;
  dimensionNameCn: string;
  satisfactionScore: number;
  description: string;
}

// 客户满意度数据
export interface CustomerSatisfactionData {
  dimensions: DimensionScore[];
  lastUpdateTime?: string;
  merchantInfo?: string;
  platform?: string;
  timeRange?: string;
}

// 平台数据
export interface PlatformData {
  [CustomerType.AllCustomers]: CustomerSatisfactionData;
  [CustomerType.ReturningCustomers]: CustomerSatisfactionData;
}

export enum PlatformType {
  Facebook = 'FACEBOOK',
  Google = 'GOOGLE',
  Xiaohongshu = 'XIAOHONGSHU',
  Yelp = 'YELP',
  AllPlatforms = 'ALL_PLATFORMS'
}

// 总返回 - 包含所有平台的数据
export interface RadarChartDataResponse {
  [PlatformType.Facebook]: PlatformData;
  [PlatformType.Google]: PlatformData;
  [PlatformType.Xiaohongshu]: PlatformData;
  [PlatformType.Yelp]: PlatformData;
  [PlatformType.AllPlatforms]: PlatformData;
}

// 平台满意度数据
export interface PlatformSatisfactionData {
  platformName: string;
  platformNameCn: string;
  satisfactionPercentage: number;
  totalReviews: number;
  satisfiedReviews: number;
}

// 客户类型满意度数据
export interface CustomerTypeSatisfactionData {
  platforms: PlatformSatisfactionData[];
}

// 平台满意度响应
export interface PlatformSatisfactionResponse {
  allCustomers: CustomerTypeSatisfactionData;
  returningCustomers: CustomerTypeSatisfactionData;
  lastUpdateTime: string;
  timeRange: string;
}

// 趋势方向枚举
export enum TrendDirection {
  UP = 'UP',
  DOWN = 'DOWN',
  FLAT = 'FLAT'
}

// 三个月满意度概况
export interface ThreeMonthOverview {
  averageSatisfactionPercentage: number;
  totalReviews: number;
  satisfiedReviews: number;
  startDate: string;
  endDate: string;
}

// 周度数据
export interface WeeklyData {
  satisfactionPercentage: number;
  totalReviews: number;
  satisfiedReviews: number;
  weekStartDate: string;
  weekEndDate: string;
  changeFromLastWeek: number;
  trendDirection: TrendDirection;
}

// 周趋势点
export interface WeeklyTrendPoint {
  weekStartDate: string;
  weekEndDate: string;
  weekLabel: string;
  satisfactionPercentage: number;
  totalReviews: number;
  satisfiedReviews: number;
}

// 客户满意度趋势数据
export interface CustomerTrendData {
  threeMonthOverview: ThreeMonthOverview;
  currentWeek: WeeklyData;
  lastWeek: WeeklyData;
  weeklyTrends: WeeklyTrendPoint[];
}

// 平台满意度趋势响应
export interface PlatformSatisfactionTrendResponse {
  allCustomers: CustomerTrendData;
  returningCustomers: CustomerTrendData;
}

// Google绑定账号状态枚举
export enum GoogleAccountStatus {
  Normal = 'NORMAL',
  Expiring = 'ABOUT_TO_EXPIRE',
  Expired = 'EXPIRED',
  SyncError = 'SYNC_EXCEPTION',
  Disabled = 'DISABLED'
}

// Google绑定账号接口
export interface GoogleAccount {
  email: string;
  status: GoogleAccountStatus;
  createTime: string;
  totalLocationCount: number;
  bondageLocationCount: number;
  notBondageLocationCount: number;
}

// 商户Google店铺位置响应
export interface GoogleMerchantLocation {
  id: number;
  title: string;
  locationId: number;
  createTime: string;
  googleEmail: number;
}

// 分页排序对象
export interface SortObject {
  empty: boolean;
  sorted: boolean;
  unsorted: boolean;
}

// 分页对象
export interface PageableObject {
  offset: number;
  sort: SortObject;
  paged: boolean;
  pageNumber: number;
  pageSize: number;
  unpaged: boolean;
}

// 分页店铺响应
export interface PageMerchantGoogleLocationResponse {
  totalPages: number;
  totalElements: number;
  size: number;
  content: GoogleMerchantLocation[];
  number: number;
  sort: SortObject;
  pageable: PageableObject;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  empty: boolean;
}

// 获取店铺列表的请求参数
export interface GetGoogleLocationsParams {
  pageNum: number;
  pageSize: number;
  merchantId?: number;
}

// Google店铺原始数据（用于 /g/locations 接口）
export interface GoogleLocationData {
  title: string;
  locationId: string;
  accountId: number;
  businessAccountId: number;
  rawData: string;
}

// 预警统计维度数据
export interface WarningStat {
  dimension: string;
  count: number;
  percent: number;
}

// 原始评论数据
export interface OriginalReview {
  name: string;
  comment: string;
  starRating: number;
  createTime: string;
  updateTime: string;
  merchantId: string;
  merchantName: string;
}

// 情感分析枚举
export enum SentimentType {
  POSITIVE = 'POSITIVE',
  NEUTRAL = 'NEUTRAL',
  NEGATIVE = 'NEGATIVE'
}

// 分析结果数据
export interface AnalysisResult {
  foodMentioned: boolean;
  foodScore: number;
  serviceMentioned: boolean;
  serviceScore: number;
  deliveryMentioned: boolean;
  deliveryScore: number;
  environmentMentioned: boolean;
  environmentScore: number;
  safetyCleanlinessMentioned: boolean;
  safetyCleanlinessScore: number;
  priceValueMentioned: boolean;
  priceValueScore: number;
  dietaryInfoMentioned: boolean;
  dietaryInfoScore: number;
  isReturningCustomer: boolean;
  overallSentiment: SentimentType;
  confidence: number;
  analysisNotes: string;
}

// 预警样本数据
export interface WarningSample {
  comment: string;
  createTime: string;
  platform: string;
  warningType: string;
  analysisId: string;
}

// 预警统计响应
export interface WarningStatsResponse {
  stats: WarningStat[];
  warningSamples: WarningSample[];
}

// 获取预警统计的请求参数
export interface GetWarningStatsParams {
  merchantId?: string;
}

// 语气选项
export interface ToneOption {
  code: number;
  name: string;
}

// 语气列表响应
export interface ToneResponse {
  tonnes: ToneOption[];
}

// 商户语气设置请求
export interface MerchantSetToneRequest {
  merchantId: number;
  toneCode: number;
  description?: string;
}

// 商户语气设置响应
export interface MerchantSettingToneResponse {
  tonnes: ToneOption[];
}

// 用户套餐信息响应
export interface UserPackageInfoResponse {
  username: string;              // 用户名
  packageName: string;           // 套餐名称
  endTime: string;              // 套餐到期时间
  daysLeft: number;             // 套餐剩余天数
  accountCount: number;         // 用户账户的账户数量
  merchantCount: number;        // 用户账户的商铺数量
  maxMerchantCount: number;     // 套餐支持的最大商铺数量
}

// 反馈类型枚举
export enum FeedbackType {
  PRODUCT = 'PRODUCT'
}

// 反馈状态枚举
export enum FeedbackStatus {
  PENDING = 0,    // 待处理
}

// 反馈请求参数
export interface FeedbackRequest {
  userId: number;                    // 用户ID
  content: string;                   // 反馈内容
  feedbackType: FeedbackType;        // 反馈类型
  merchantId: number;                // 商户ID
}

// 反馈响应
export interface FeedbackResponse {
  id: number;                        // 反馈ID
  userId: number;                    // 用户ID
  content: string;                   // 反馈内容
  feedbackType: string;              // 反馈类型代码
  feedbackTypeName: string;          // 反馈类型名称
  status: FeedbackStatus;            // 状态
  statusName: string;                // 状态名称
  merchantId: number;                // 商户ID
  createTime: string;                // 创建时间
  updateTime: string;                // 更新时间
}

// 消息类型
export interface Message {
  id: number;
  title: string;
  content: string;
  type: 'system' | 'notification' | 'alert';
  isRead: boolean;
  createTime: string;
}

// 消息列表响应
export interface MessageListResponse {
  messages: Message[];
  total: number;
  unreadCount: number;
}

// 系统消息
export interface SystemMessage {
  id: number;                    // 主键id
  title: string;                 // 标题
  content: string;               // 消息内容
  readFlag: boolean;             // 状态,true标识已读
  createTime: string;            // 创建时间
}

// 系统消息列表响应
export interface PageSystemMessageResponse {
  totalPages: number;
  totalElements: number;
  size: number;
  content: SystemMessage[];
  number: number;
  sort: SortObject;
  pageable: PageableObject;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  unpaged: boolean;
  empty: boolean;
}

// 系统消息列表请求参数
export interface GetSystemMessageParams {
  pageNum: number;
  pageSize: number;
}


// 系统消息已读状态请求
export interface SystemMessageReadRequest {
  id: number;                    // 主键id
}

// 发送重置密码验证码请求
export interface ResetPasswordSendRequest {
  email: string;
}

// 验证重置密码验证码请求
export interface ResetPasswordVerifyRequest extends ResetPasswordSendRequest {
  code: string;
}

// 重置密码请求
export interface ResetPasswordResetRequest extends ResetPasswordVerifyRequest {
  state: string;
  newPassword: string;
}

// 用户偏好设置更新类型枚举
export enum UserPreferenceUpdateType {
  LANGUAGE = 'LANGUAGE',
  TIME_ZONE = 'TIME_ZONE'
}

// 用户偏好设置更新请求
export interface UserPreferSettingUpdateRequest {
  userId: number;
  updateType: UserPreferenceUpdateType;
  preferLanguage?: string;
  preferTimeZone?: string;
}

// 支持的语言枚举
export enum LanguageCode {
  ZH_CN = 'zh-CN',  // 简体中文
  ZH_TW = 'zh-TW',  // 繁体中文
  EN_US = 'en-US',  // 英语(美国)
  JA_JP = 'ja-JP'   // 日语
}

// 用户信息响应接口
export interface UserInfoResponse {
  userId: number;           // 用户ID
  username: string;         // 用户名
  preferTimeZone?: string;  // 首选时区
  preferLanguage?: string;  // 首选语言
}

// Remember Me 相关接口
export interface RememberMeData {
  token: string;
  username: string;
  password: string;
  timestamp: number;
}

// Remember Me 常量
export const REMEMBER_ME_KEY = 'rememberMe';
export const REMEMBER_ME_EXPIRY_DAYS = 7;
