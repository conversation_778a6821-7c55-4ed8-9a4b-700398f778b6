// Dashboard API 响应类型

import { PeriodType } from "./review";
import { Merchant } from "./merchant";


export interface StatisticsResponse {
  total: number;
  normal: number;
  abnormal: number;
  comparedToYesterday: number;
  abnormalMerchant: Merchant[];
}

export interface AllCommentsRequest {
  startDate: string;
  endDate: string;
  merchantId?: number;
  type: 'select' | 'custom';
}

// 定义接口返回类型
export interface AllCommentsResponse {
  toDayComments: number;
  comparedToYesterday: number;
  weekGood: number;
  allComments: {
    merchantTypeName: string;
    totalComments: number;
  }[];
}

// 每日满意度统计数据
export interface DailySatisfactionData {
  date: string; // 统计日期
  totalReviews: number; // 总评论数
  positiveReviews: number; // 好评数 (满意度得分>=4分)
  negativeReviews: number; // 差评数 (满意度得分<4分)
  satisfactionPercentage: number; // 满意度百分比 (好评/(好评+差评)*100)
}

// 统计汇总信息
export interface StatisticsSummary {
  totalReviews: number; // 总评论数
  totalPositiveReviews: number; // 总好评数
  totalNegativeReviews: number; // 总差评数
  overallSatisfactionPercentage: number; // 整体满意度百分比
  statisticsDays: number; // 统计天数
  averageDailyReviews: number; // 平均每日评论数
}
export interface CustomerSatisfactionStatisticsRequest {
  startTime?: string;
  endTime?: string;
  periodType: PeriodType | 'CUSTOM';
  agentId?: number;
}
// 客户满意度统计响应类型
export interface CustomerSatisfactionStatisticsResponse {
  dailyStatistics: DailySatisfactionData[]; // 每日满意度统计数据
  summary: StatisticsSummary; // 汇总信息
  lastUpdateTime: string; // 数据更新时间
  timeRange: string; // 数据统计时间范围
}
