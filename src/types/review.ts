// 星级评分枚举
export enum StarRating {
  STAR_RATING_UNSPECIFIED = 'STAR_RATING_UNSPECIFIED',
  ONE = 'ONE',
  TWO = 'TWO',
  THREE = 'THREE',
  FOUR = 'FOUR',
  FIVE = 'FIVE'
}

export enum PlatformEnums {
  Google = 'GOOGLE',
  Xiaohongshu = 'XIAOHONGSHU',
  Yelp = 'YELP',
  Facebook = 'FACEBOOK',
  TikTok = 'TIKTOK',
  IG = 'IG',
  Amazon = 'AMAZON',
  Other = 'OTHER'
}

// 评论者接口
export interface Reviewer {
  profilePhotoUrl: string;
  displayName: string;
  isAnonymous: boolean;
}

// 评论回复接口
export interface ReviewReply {
  comment: string;
  updateTime: string;
}

// 评论接口
export interface Review {
  name: string;
  reviewId: string;
  reviewer: Reviewer;
  starRating: StarRating;
  comment: string;
  createTime: string;
  updateTime: string;
  // TODO 商家回复应该是数组
  reviewReply?: ReviewReply[];
}

// 评论列表接口
export interface ReviewList {
  reviews: Review[];
  averageRating: number;
  totalReviewCount: number;
  nextPageToken?: string;
}

// 回复表单数据
export interface ReplyFormData {
  reviewId: string;
  content: string;
}

// 评论筛选参数
export interface ReviewFilters {
  platform?: string;
  rating?: StarRating;
  dateRange?: [string, string];
  keyword?: string;
}

// 评论统计数据
export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  platformBreakdown: Record<string, number>;
  ratingBreakdown: Record<StarRating, number>;
}

export interface GoogleReviewReplyParams {
  id: number;
  replyContent: string;
  aiTag: boolean;
}

export interface GetGoogleReviewsParams {
  merchantLocationId: number;
  pageNum?: number;
  pageSize?: number;
}

export interface GetGoogleReviewsResponseContent {
  id: number;
  evaluateName: string;
  evaluateUrl?: string;
  anonymousFlag: boolean;
  starRating: string;
  content: string;
  commentUpdateTime: string;
  replyContent: string;
  replyUpdateTime: string;
  aiTag: boolean;
}

export interface GetGoogleReviewsResponse {
  totalPages: number;
  totalElements: number;
  size: number;
  content: GetGoogleReviewsResponseContent[];
  number: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  pageable: {
    offset: number;
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    paged: boolean;
    pageNumber: number;
    pageSize: number;
    unpaged: boolean;
  };
  numberOfElements: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}

// 时间周期枚举
export enum PeriodType {
  TODAY = 'TODAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  THREE_MONTHS = 'THREE_MONTHS',
  PAST_7_DAYS = 'PAST_7_DAYS',
  PAST_30_DAYS = 'PAST_30_DAYS',
  PAST_90_DAYS = 'PAST_90_DAYS'
}

export enum LowerCasePlatformEnums {
  google = 'google',
  xiaohongshu = 'xiaohongshu',
  yelp = 'yelp',
  facebook = 'facebook',
  tiktok = 'tiktok',
  ig = 'ig',
  amazon = 'amazon',
  other = 'other'
}

// 获取评论统计参数
export interface GetReviewStatisticsParams {
  periodType: PeriodType;
  merchantId?: number;
  agentId?: number;
}

// 每日评论统计
export interface DailyReviewStatistics {
  date: string;
  positiveCount: number;
  negativeCount: number;
  totalCount: number;
}

// 平台评论统计
export interface PlatformReviewStatistics {
  positiveCount: number;
  negativeCount: number;
  totalCount: number;
  dailyStatistics: DailyReviewStatistics[];
}

// 评论统计响应
export interface ReviewStatisticsResponse {
  totalPositiveCount: number;
  totalNegativeCount: number;
  statistics: Record<string, PlatformReviewStatistics>;
}