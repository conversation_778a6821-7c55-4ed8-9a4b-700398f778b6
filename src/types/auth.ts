import { ReactNode } from 'react';
import { LanguageCode } from './api';

export interface loginParams {
  account: string;
  password: string;
}

// 用户接口
export interface User {
  userId: number;
  username: string;
  token: string;
  agentId?: number;
  merchantId?: number;
}

export enum RoleCodeEnums {
  AGENT = 'AGENT',
  MERCHANT = 'MERCHANT'
}

interface BaseRole {
  id: number;
  roleName: string;
  roleCode: RoleCodeEnums;
  description: string;
  status: number;
  createTime: string;
  createUserId: number | null;
  updateTime: string;
  updateUserId: number | null;
}

// 角色接口
export interface Role extends BaseRole {
  agentId?: number;
}

// 用户上下文接口
export interface UserContext {
  user: Role;
  isAgent: boolean;
  currentRole: Role | null;
  isMerchant: boolean;
  username: string;
  availableRoles: Role[];
  switchRole: (roleId: number) => Promise<boolean>;
  clearRole: () => Promise<boolean>;
}

// 切换角色参数
export interface SwitchRoleParams {
  roleId: number;
}

// 菜单项接口
export interface MenuItem {
  id: number;
  parentId: number;
  menuName: string;
  menuCode: string;
  path: string;
  component: string;
  redirect: string;
  icon: string;
  sort: number;
  isHidden: boolean;
  isCache: boolean;
  isHome: boolean;
  status: number;
  createTime: string;
  children?: MenuItem[];
}


export interface UserContextWithRoleIds extends UserContext {
  merchantRoleId: number;
  agentRoleId?: number;
  agentId?: number;
}

// 认证上下文类型
export interface AuthContextType {
  currentUser: User | null;
  userContext: UserContextWithRoleIds | null;
  login: (account: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  logout: () => Promise<void>;
  loading: boolean;
  menus: MenuItem[];
  switchRole: (roleId?: number, merchantId?: number) => Promise<boolean>;
  clearRole: () => Promise<void>;
  merchantId?: number;
  agentId?: number;
  clearAuth: () => void;
  temporaryLanguage: LanguageCode | undefined;
  setTemporaryLanguage: (language: LanguageCode | undefined) => void;
  autoLogin: () => Promise<boolean>;
}

// 认证提供者属性
export interface AuthProviderProps {
  children: ReactNode;
}

// 登录表单数据
export interface LoginFormData {
  account: string;
  password: string;
  remember?: boolean;
} 