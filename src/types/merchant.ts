import { PlatformEnums } from "./review";

// 商户查询参数
export interface MerchantSearchParams {
  title?: string;
  pageNum?: number;
  pageSize?: number;
}

// API响应中的商户数据格式（用于统计接口）
export interface Merchant {
  id: number;
  title: string;
}

// 商户历史记录项
export interface MerchantHistoryItem {
  id: number;
  merchantId: number;
  title: string;
}

// 排序信息
export interface SortInfo {
  empty: boolean;
  sorted: boolean;
  unsorted: boolean;
}

// 分页信息
export interface PageableInfo {
  offset: number;
  sort: SortInfo;
  paged: boolean;
  pageNumber: number;
  pageSize: number;
  unpaged: boolean;
}

// 商户历史记录分页响应格式
export interface MerchantHistoryResponse {
  totalPages: number;
  totalElements: number;
  size: number;
  content: MerchantHistoryItem[];
  sort: SortInfo;
  pageable: PageableInfo;
}

export interface MerchantLocation {
  id: number;
  type: PlatformEnums;
  name: string;
}
