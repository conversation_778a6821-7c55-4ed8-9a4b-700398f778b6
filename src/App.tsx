import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { AuthProvider } from './contexts/AuthContext';
import { MerchantProvider } from './contexts/MerchantContext';
import routes from './routes';
import './ant-reset.css';
import { useTranslation } from 'react-i18next';

import zhCN from 'antd/locale/zh_CN';
import zhTW from 'antd/locale/zh_TW';
import enUS from 'antd/locale/en_US';
import jaJP from 'antd/locale/ja_JP';

// 创建路由器
const router = createBrowserRouter(routes);

// 语言到 Ant Design locale 的映射
const antdLocaleMap = {
  zh: zhCN,
  'zh-TW': zhTW,
  en: enUS,
  ja: jaJP
};

function App() {
  const { i18n } = useTranslation();

  // 获取对应的 Ant Design locale
  const antdLocale = antdLocaleMap[i18n.language as keyof typeof antdLocaleMap] || zhCN;

  return (
    <ConfigProvider
      locale={antdLocale}
      theme={{
        token: {
          colorPrimary: '#0D728F' // 全局主色调
        },
        components: {
          Button: {
            colorPrimary: '#0D728F' // 按钮主色
          },
          Tabs: {
            colorPrimary: '#0D728F', // Tab 主色
            inkBarColor: '#0D728F', // 墨水条颜色
            itemSelectedColor: '#ffffff', // 选中状态文字颜色
            itemHoverColor: '#0D728F' // 悬停状态文字颜色
          },
          Menu: {
            colorPrimary: '#0D728F',
            itemSelectedBg: '#b6354c', // 菜单选中背景色
            itemSelectedColor: '#ffffff', // 菜单选中文字色
            itemHoverBg: 'rgba(182, 53, 76, 0.1)', // 菜单悬停背景色
            itemHoverColor: '#b6354c' // 菜单悬停文字色
          },
          Pagination: {
            colorPrimary: '#0D728F',
            itemActiveBg: '#e4f5f5' // 分页激活背景色
          }
        }
      }}
    >
      <AuthProvider>
        <MerchantProvider>
          <RouterProvider router={router} />
        </MerchantProvider>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
