{"common": {"loading": "Loading...", "noData": "No data available", "cancel": "Cancel", "submit": "Submit", "viewAll": "View all", "back": "Back", "merchant": "Owner", "confirm": "Confirm", "paginationTotal": "{{start}}-{{end}} of {{total}} items", "noMoreMessages": "No more messages", "loadMore": "Loading more...", "noMessages": "No message", "loadMessagesFailed": "Failed to load messages"}, "navigation": {"home": "Home", "satisfaction": "Customer Satisfaction", "analysis": "Review Insights", "reviews": "Review Reply MGT", "messages": "Alert Center", "settings": "Customized Settings", "auth": "Authorization settings", "package": "Service Bundle", "messageCenter": "Message Center", "logout": "Log out", "welcome": "Welcome, {{username}}", "agentPortal": "Agent Portal", "systemTitle": "Location Review & User Satisfaction Mgmt Platform", "hello": "Hello, {{username}}", "userName": "User"}, "login": {"forgotPassword": "Forgotten Password?", "forgotPasswordTitle": "Forgotten Password", "contactUs": "Contact Us", "loginSuccessful": "Login successful!", "loginToYourAccount": "Log into your Account", "orSignInWithEmail": "------------- or Sign in with Email -------------", "email": "Email", "password": "Password", "pleaseInputEmail": "Please input your Email!", "pleaseInputPassword": "Please input your Password!", "login": "Log in", "changePassword": "Change Password", "enterEmail": "<PERSON><PERSON>", "enterValidEmail": "Please enter a valid email address", "sendVerificationCode": "Send verification code", "verificationCode": "Verification code", "enterVerificationCode": "Please enter verification code", "verificationCodeSent": "The verification code has been sent", "verificationSuccessful": "Verification was successful", "newPassword": "New Password", "enterNewPassword": "Please enter a new password", "passwordRequirements": "The password must include uppercase letters, lowercase letters, numbers, and be at least 8 characters long", "confirmPassword": "Confirm Password", "enterPasswordAgain": "Please re-enter your password", "passwordsDoNotMatch": "Passwords do not match", "passwordResetSuccessful": "Password reset successful", "goBack": "Go Back", "pleaseEnterEmailFirst": "Please enter your email first"}, "home": {"todayStats": "Today's Statistics", "customerSatisfaction": "Customer Satisfaction", "customerSatisfactionTrend": "Customer Satisfaction Trend", "overallCustomerSatisfaction": "Overall Customer Satisfaction", "thisWeekSatisfaction": "Weekly Satisfaction", "lastWeekSatisfaction": "Last Week's Satisfaction", "satisfactionChange": "Satisfaction variations", "positiveReviews": "High Rating", "negativeReviews": "Low Rating", "satisfaction": "Satisfaction", "quantity": "Sum", "customerSatisfactionStatistics": "Customer Satisfaction Statistics", "abnormalMerchants": "Customer satisfaction status error", "statusAbnormalMerchants": "Customer satisfaction status error", "comparedToLastWeek": "WoW (Week over week)", "warningCategories": "Alert sort", "monthlyWarningMessages": "Monthly Alert", "noWarnings": "No Alert", "warningMessages": "<PERSON><PERSON><PERSON>", "noWarningData": "No Alert", "comparedToYesterday": "yesterday", "count": "count", "merchantName": "Location Name", "selectMerchant": "Select Location", "enterMerchant": "Enter Location", "merchantStatistics": "Location Statistics", "merchantManagementTotal": "Total", "todayReviews": "Today's Reviews", "todayReviewTotal": "Total", "todayNegativeReviewMerchants": "Today's Low-Rating Location", "positiveReviewCount": "High-rating Review Count", "negativeReviewCount": "Low-rating Review Count", "viewAllReviewStatistics": "View All Review Statistics", "merchantReviewsStatistics": "Merchant Reviews Statistics", "reviewCount": "Review Count", "todayTotalReviews": "Today's Total Reviews", "weekGoodRate": "Weekly High-rating", "viewAllReviews": "View All Reviews", "normalCount": "Normal Count", "abnormalCount": "Abnormal Count", "noAbnormalMerchants": "No abnormal merchants! Great!", "returningCustomers": "Returning Customers", "allCustomers": "All Customers", "totalReviews": "Total Reviews", "compared": "Compared", "rememberMe": "Remember Me", "autoLoginSuccessful": "Auto login successful", "checkingLoginStatus": "Checking login status..."}, "package": {"upgradePackage": "Upgrade Service Bundle", "currentPackage": "Current Service Bundle", "packageName": "Bundle Name", "packagePrice": "Bundle Price", "packageDuration": "Bundle Duration", "packageFeatures": "Bundle Features", "upgradeNow": "Upgrade Now", "contactSales": "Contact Sales", "platformAccountLogin": "Platform Account Login", "expiresIn": " Expires {{date}}", "daysLeft": "{{days}} Days left", "currentAccountBenefits": "Current Account Benefits", "storeCount": "Location Count", "accountCount": "Account Count", "noPackageInfo": "No Service Bundle information"}, "review": {"reply": "Reply", "edit": "Edit", "replyComment": "Reply to comment", "replySuccess": "Reply successful", "pleaseEnterReplyContent": "Please enter reply content", "generatingAIReply": "Generating AI reply...", "editTime": "Edit Time", "lastEditTime": "Last Edit Time"}, "warning": {"fetchWarningMessagesFailed": "Failed to fetch <PERSON><PERSON>", "noWarningMessages": "No alert now", "noMoreWarningMessages": "No more alerts", "warningMessageDetail": "Alert content", "warningMessage": "Alert: {{analysisNotes}}. Low Rating Content: {{reviewComment}}", "negativeReviewContent": "Low Rating Content", "warningMessageOnly": "<PERSON><PERSON>"}, "analysis": {"reviewCount": "Review Count", "positiveReviews": "High Rating Reviews", "negativeReviews": "Low Rating Reviews", "platformReviewCountAnalysis": "Cross-Platform Review Insights", "overallReviewAnalysis": "Review Overall Insights"}, "platforms": {"google": "Google", "facebook": "Facebook", "yelp": "Yelp", "xiaohongshu": "rednote", "amazon": "Amazon", "other": "Other"}, "authManagement": {"googleAuthManagement": "Google Authorization Mgmt", "processingGoogleAuth": "Processing Google authorization...", "accountHasAbnormal": "There are abnormal accounts in the current authorization, please handle them as soon as possible", "authorizedAccounts": "Authorized Accounts", "bindNewAccount": "<PERSON> Account", "authorizedStores": "Authorized Location", "authorizedAccount": "Authorized Account", "status": "Status", "storeCount": "Location Count", "bindingTime": "Time Linked", "boundStores": "Linked Location", "actions": "Process", "rebind": "Link", "storeName": "Location Name", "associatedGoogleAccount": "Associated Google Account", "unbind": "Unlink", "unbindSuccess": "Unlink Successful", "bindNewAccountTitle": "<PERSON> Account", "rebindAccountOperation": "Relink Account Operation", "rebindAccountDescription": "Rebinding your account allows you to select more merchants to add", "pleaseSelectAtLeastOneStore": "Please select at least one location to link", "allSelectedStoresAlreadyBound": "All selected locations are already linked", "successfullyBoundStores": "Successfully linked to {{count}} locations!", "bindStoresFailed": "Failed to link location, please try again", "getGoogleAuthUrlFailed": "Failed to get Google authorization URL", "getMerchantSelectionDataFailed": "Failed to get location data", "googleAccountBindingSuccess": "Google account linking successful!", "oneClickBind": "One-Click Link", "useGoogleAccountLogin": "Use Google Account Login", "unbindAccount": "Unlink Account", "unbindAccountDescription": "Unbinding your account is irreversible. Features such as automatic replies will be suspended. Existing comments on Google platforms will not be affected. If you need to manage them after unbinding, you will need to rebind", "authorizingGoogleAccount": "Authorizing Google Account", "selectStoreToBind": "Select location to Link", "selectAll": "Select All", "abnormalStatusTip": "Account status error, please handle it promptly", "expiring": "Expiring", "expired": "Expired", "disabled": "Disabled", "syncException": "Sync Exception", "normal": "Normal", "rebindNewAccount": "This operation will rebind the account"}, "settings": {"personalizationSettings": "Personalization Settings", "morePersonalizationSuggestions": "More personalization needs, please click feedback", "feedbackMore": "Feed<PERSON>", "selectReplyTone": "Select Reply <PERSON>", "selectTone": "<PERSON>", "productFeedback": "Product Feedback", "thankYouForValuableFeedback": "Thank you for your valuable suggestions, our products will continue to improve", "enterPersonalizationRequirements": "Please enter your personalization needs", "pleaseEnterFeedbackContent": "Please enter feedback", "userOrMerchantInfoFailed": "User or location information failed to fetch", "feedbackSubmittedSuccessfully": "<PERSON><PERSON><PERSON> submitted successfully", "feedbackSubmitFailed": "Feed<PERSON> submission failed", "merchantInfoFailed": "Location information failed to fetch", "settingsSaved": "Your setting have saved", "setToneTo": "Tone set to: {{tone}}", "setToneFailed": "Failed to set tone"}, "time": {"today": "Today", "yesterday": "Yesterday", "updated": "updated", "week": "This Week", "currentDay": "Current Day", "currentWeek": "Current Week", "month": "This Month", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days"}, "satisfaction": {"customerSatisfaction": "Customer Satisfaction", "allCustomersSatisfaction": "Overall Customers Satisfaction", "returningCustomersSatisfaction": " Loyal Customers Satisfaction", "last90DaysData": "Last 90 Days Data", "satisfaction": "Customer Satisfaction", "ALL_PLATFORMS": "Multi-dimensional Customer Satisfaction", "GOOGLE": "Google Customer Satisfaction", "YELP": "Yelp Customer Satisfaction", "XIAOHONGSHU": "rednote Customer Satisfaction", "FACEBOOK": "Facebook Customer Satisfaction"}, "language": {"zhCN": "Simplified Chinese", "zhTW": "Traditional Chinese", "enUS": "English", "jaJP": "Japanese"}}