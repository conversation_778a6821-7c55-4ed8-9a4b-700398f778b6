import { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, Tabs, Table, Button, Alert, Tag, Modal, message, Checkbox, Space, Radio } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  getAllGoogleAccounts,
  getGoogleLocations,
  getGoogleAuthUrl,
  submitGoogleAuth,
  unbindGoogleLocation,
  getGoogleLocationsByAccountId,
  getAllGoogleLocationsByAccount,
  bindGoogleLocations
} from '@/api';
import { GoogleAccountStatus, PlatformType, type GoogleAccount, type GoogleMerchantLocation, type GoogleLocationData } from '@/types';
import WarningIcon from '@/assets/svg/warning-yellow.svg';
import AlertIcon from '@/assets/svg/alert-error.svg';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';

// 授权店铺数据类型 - 基于API返回的MerchantGoogleLocationResponse
interface AuthStoreData {
  key: string;
  [key: string]: any; // 由于API文档显示为通用对象，暂时使用灵活的类型定义
}

const statusMap = {
  [GoogleAccountStatus.Normal]: { bg: '#F2F3F5', text: '#1D2129' },
  [GoogleAccountStatus.Expired]: {
    bg: '#FFF5F0',
    text: '#A43420'
  },
  [GoogleAccountStatus.Disabled]: {
    bg: '#FFF5F0',
    text: '#A43420'
  },
  [GoogleAccountStatus.Expiring]: {
    bg: '#FCFAED',
    text: '#966D1B'
  },
  [GoogleAccountStatus.SyncError]: {
    bg: '#FFF5F0',
    text: '#A43420'
  }
};

// 获取状态显示文本的函数
const getStatusText = (status: GoogleAccountStatus, t: any) => {
  switch (status) {
    case GoogleAccountStatus.Normal:
      return t('authManagement.normal');
    case GoogleAccountStatus.Expired:
      return t('authManagement.expired');
    case GoogleAccountStatus.Disabled:
      return t('authManagement.disabled');
    case GoogleAccountStatus.Expiring:
      return t('authManagement.expiring');
    case GoogleAccountStatus.SyncError:
      return t('authManagement.syncException');
    default:
      return status;
  }
};

export const Auth = () => {
  const { merchantId, agentId } = useAuth();
  const isMerchant = Boolean(merchantId);
  const isAgentReviewMerchant = Boolean(agentId) && isMerchant;
  const { t } = useTranslation();

  const [activeTab, setActiveTab] = useState(PlatformType.Google);
  const [unbindModalVisible, setUnbindModalVisible] = useState(false);
  const [bindNewAccountModalVisible, setBindNewAccountModalVisible] = useState(false);
  const [rebindConfirmModalVisible, setRebindConfirmModalVisible] = useState(false);
  const [merchantSelectionModalVisible, setMerchantSelectionModalVisible] = useState(false);
  const [selectedStore, setSelectedStore] = useState<AuthStoreData | null>(null);
  const [authAccountData, setAuthAccountData] = useState<GoogleAccount[]>([]);
  const [authStoreData, setAuthStoreData] = useState<AuthStoreData[]>([]);
  const [loading, setLoading] = useState(false);
  const [storeLoading, setStoreLoading] = useState(false);
  const [hasAbnormalAccount, setHasAbnormalAccount] = useState(false);
  const [authProcessing, setAuthProcessing] = useState(false);
  const [storePagination, setStorePagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 商户选择Modal相关状态
  const [currentGoogleAccountEmail, setCurrentGoogleAccountEmail] = useState<string>('');
  const [allGoogleLocations, setAllGoogleLocations] = useState<GoogleLocationData[]>([]);
  const [boundGoogleLocations, setBoundGoogleLocations] = useState<GoogleMerchantLocation[]>([]);
  const [selectedLocationIds, setSelectedLocationIds] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [merchantSelectionLoading, setMerchantSelectionLoading] = useState(false);

  // 解析URL参数
  const getUrlParams = (): { state?: string; code?: string } => {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      state: urlParams.get('state') || undefined,
      code: urlParams.get('code') || undefined
    };
  };

  // 清理URL参数
  const clearUrlParams = () => {
    const url = new URL(window.location.href);
    url.searchParams.delete('state');
    url.searchParams.delete('code');
    url.searchParams.delete('scope');
    url.searchParams.delete('authuser');
    url.searchParams.delete('prompt');
    window.history.replaceState({}, document.title, url.toString());
  };

  // 处理Google OAuth回调
  const handleGoogleCallback = useCallback(async () => {
    const { state, code } = getUrlParams();

    if (state && code) {
      setAuthProcessing(true);

      try {
        // 调用授权接口
        const { id, email } = (await submitGoogleAuth(code, state)) || {};

        if (id) {
          message.success(t('authManagement.googleAccountBindingSuccess'));
          setCurrentGoogleAccountEmail(email || '');
          // 清理存储的state
          localStorage.removeItem('googleAuthState');
          // 调用两个接口获取店铺数据
          await fetchMerchantSelectionData(id);
          // 显示商户选择Modal
          setMerchantSelectionModalVisible(true);
          // 关闭绑定弹窗（如果打开的话）
          setBindNewAccountModalVisible(false);
          setRebindConfirmModalVisible(false);
        }
      } finally {
        setAuthProcessing(false);
        // 清理URL参数
        clearUrlParams();
      }
    }
  }, []);

  // 获取商户选择数据
  const fetchMerchantSelectionData = async (token: number) => {
    setMerchantSelectionLoading(true);
    try {
      const [allLocations, boundLocations] = await Promise.all([
        getAllGoogleLocationsByAccount(token),
        getGoogleLocationsByAccountId(token)
      ]);

      setAllGoogleLocations(allLocations);
      setBoundGoogleLocations(boundLocations);

      // 设置已绑定的店铺为选中状态
      const boundLocationIds = boundLocations.map(location => location.locationId.toString());
      setSelectedLocationIds(boundLocationIds);

      // 检查是否全选
      setSelectAll(boundLocationIds.length === allLocations.length);
    } catch (error) {
      console.error(t('authManagement.getMerchantSelectionDataFailed'), error);
    } finally {
      setMerchantSelectionLoading(false);
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    // 商户模式下不支持全选
    if (isMerchant) return;

    setSelectAll(checked);
    if (checked) {
      setSelectedLocationIds(allGoogleLocations.map(location => location.locationId));
    } else {
      setSelectedLocationIds(boundGoogleLocations.map(location => location.locationId.toString()));
    }
  };

  // 处理单个店铺选择
  const handleLocationSelect = (locationId: string, checked: boolean) => {
    let newSelectedIds: string[] = [];

    if (isMerchant) {
      // 商户模式：单选
      if (checked) {
        newSelectedIds = [locationId]; // 只保留当前选中的
      } else {
        newSelectedIds = [];
      }
    } else {
      // Agent模式：多选
      if (checked) {
        newSelectedIds = [...selectedLocationIds, locationId];
      } else {
        newSelectedIds = selectedLocationIds.filter(id => id !== locationId);
      }
    }

    setSelectedLocationIds(newSelectedIds);

    // 更新全选状态（仅在Agent模式下有效）
    if (!isMerchant) {
      setSelectAll(newSelectedIds.length === allGoogleLocations.length);
    }
  };

  // 关闭商户选择Modal
  const handleMerchantSelectionCancel = () => {
    setMerchantSelectionModalVisible(false);
    setCurrentGoogleAccountEmail('');
    setAllGoogleLocations([]);
    setBoundGoogleLocations([]);
    setSelectedLocationIds([]);
    setSelectAll(false);
    // 重新获取账号数据
    fetchGoogleStores();
    fetchGoogleAccounts();
  };

  // 处理商户选择确认（提交绑定逻辑）
  const handleMerchantSelectionConfirm = async () => {
    if (selectedLocationIds.length === 0) {
      message.warning(t('authManagement.pleaseSelectAtLeastOneStore'));
      return;
    }

    // 排除已经绑定的店铺
    const boundLocationIds = boundGoogleLocations.map(location => location.locationId.toString());
    const newLocationIds = selectedLocationIds.filter(id => !boundLocationIds.includes(id));

    // 如果排除后没有新的店铺需要绑定
    if (newLocationIds.length === 0) {
      message.info(t('authManagement.allSelectedStoresAlreadyBound'));
      return;
    }
    const selectedLocations = allGoogleLocations?.filter(location => newLocationIds.includes(location.locationId));

    setMerchantSelectionLoading(true);
    try {
      await bindGoogleLocations({
        type: activeTab,
        locations: selectedLocations
      });

      message.success(t('authManagement.successfullyBoundStores', { count: selectedLocations.length }));
      handleMerchantSelectionCancel();
    } catch (error) {
      console.error(t('authManagement.bindStoresFailed'), error);
      message.error(t('authManagement.bindStoresFailed'));
    } finally {
      setMerchantSelectionLoading(false);
    }
  };

  // 获取Google账号数据
  const fetchGoogleAccounts = async () => {
    setLoading(true);
    try {
      const accounts = await getAllGoogleAccounts();
      setAuthAccountData(accounts);
      setHasAbnormalAccount(
        accounts.some(account => account.status !== GoogleAccountStatus.Normal && account.status !== GoogleAccountStatus.Expiring)
      );
    } finally {
      setLoading(false);
    }
  };

  // 获取Google店铺数据
  const fetchGoogleStores = async (pageNum: number = 1, pageSize: number = 10) => {
    setStoreLoading(true);
    try {
      const response = await getGoogleLocations({ pageNum: pageNum - 1, pageSize, merchantId: merchantId as number });
      if (response) {
        const formattedData: AuthStoreData[] = response.content.map((store, index) => ({
          key: `${pageNum}-${index}`,
          ...store
        }));
        setAuthStoreData(formattedData);
        setStorePagination({
          current: response.number + 1, // API返回的number是0基的
          pageSize: response.size,
          total: response.totalElements
        });
      }
    } finally {
      setStoreLoading(false);
    }
  };

  // 确认重新绑定 - 打开一键绑定弹窗
  const handleRebindConfirm = () => {
    setRebindConfirmModalVisible(false);
    setBindNewAccountModalVisible(true);
  };

  // 组件挂载时处理Google OAuth回调和获取数据
  useEffect(() => {
    // 首先处理Google OAuth回调
    handleGoogleCallback();

    // 然后获取常规数据
    if (activeTab === PlatformType.Google) {
      fetchGoogleAccounts();
      fetchGoogleStores();
    }
  }, [activeTab, handleGoogleCallback]);

  // 处理店铺分页变化
  const handleStoreTableChange = (pagination: any) => {
    fetchGoogleStores(pagination.current, pagination.pageSize);
  };

  // 授权账号表格列配置
  const authAccountColumns: ColumnsType<GoogleAccount> = [
    {
      title: t('authManagement.authorizedAccount'),
      dataIndex: 'email'
    },
    {
      title: t('authManagement.status'),
      dataIndex: 'status',
      render: (_, record: GoogleAccount) => {
        return (
          <Tag
            style={{
              backgroundColor: statusMap[record.status]?.bg,
              color: statusMap[record.status]?.text,
              border: 'none'
            }}
          >
            {getStatusText(record.status, t)}
          </Tag>
        );
      }
    },
    {
      title: t('authManagement.storeCount'),
      dataIndex: 'totalLocationCount'
    },
    {
      title: t('authManagement.bindingTime'),
      dataIndex: 'createTime'
    },
    {
      title: t('authManagement.boundStores'),
      dataIndex: 'bondageLocationCount',
      align: 'right',
      render: (_, record: GoogleAccount) => {
        return (
          <span>
            {record.bondageLocationCount}/{record.totalLocationCount}
          </span>
        );
      }
    },
    {
      title: t('authManagement.actions'),
      key: 'action',
      render: () => (
        <Button type="link" size="small" onClick={() => setRebindConfirmModalVisible(true)}>
          {t('authManagement.rebind')}
        </Button>
      )
    }
  ];

  // 授权店铺表格列配置 - 基于实际API返回数据
  const authStoreColumns: ColumnsType<AuthStoreData> = [
    {
      title: t('authManagement.storeName'),
      dataIndex: 'title' // 假设API返回中有name字段
    },
    {
      title: t('authManagement.associatedGoogleAccount'),
      dataIndex: 'googleEmail'
    },
    {
      title: t('authManagement.bindingTime'),
      dataIndex: 'createTime'
    },
    {
      title: t('authManagement.actions'),
      render: (record: AuthStoreData) => (
        <Button type="link" size="small" onClick={() => showUnbindModal(record)}>
          {t('authManagement.unbind')}
        </Button>
      ),
      hidden: isAgentReviewMerchant
    }
  ].filter(column => !column?.hidden);

  // 处理解绑确认
  const handleUnbindConfirm = useCallback(async () => {
    await unbindGoogleLocation(selectedStore?.id);
    setUnbindModalVisible(false);
    setSelectedStore(null);
    message.success(t('authManagement.unbindSuccess'));
    fetchGoogleStores();
    fetchGoogleAccounts();
  }, [selectedStore, t]);

  // 处理解绑取消
  const handleUnbindCancel = () => {
    setUnbindModalVisible(false);
    setSelectedStore(null);
  };

  // 显示解绑确认弹窗
  const showUnbindModal = (record: AuthStoreData) => {
    setSelectedStore(record);
    setUnbindModalVisible(true);
  };

  const onBindNewAccount = () => {
    setBindNewAccountModalVisible(true);
  };

  // 处理绑定新账户弹窗关闭
  const handleBindNewAccountCancel = () => {
    setBindNewAccountModalVisible(false);
  };

  // 处理Google登录
  const handleGoogleLogin = useCallback(async () => {
    try {
      const url = await getGoogleAuthUrl();
      window.open(url, '_self');
    } catch (error) {
      console.error(t('authManagement.getGoogleAuthUrlFailed'), error);
    }
  }, [t]);

  const tabItems = useMemo(
    () => [
      {
        key: PlatformType.Google,
        label: t('authManagement.googleAuthManagement'),
        children: (
          <div>
            {authProcessing && <Alert message={t('authManagement.processingGoogleAuth')} type="info" className="mb-4" />}
            {hasAbnormalAccount && (
              <Alert
                message={t('authManagement.accountHasAbnormal')}
                showIcon
                type="error"
                closable
                className="mb-4"
                icon={<img src={AlertIcon} alt="alert" className="w-[13px] h-[13px]" />}
              />
            )}
            {/* 授权账号部分 */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">{t('authManagement.authorizedAccounts')}</h3>
                <Button type="primary" icon={<PlusOutlined />} onClick={onBindNewAccount} disabled={authProcessing}>
                  {t('authManagement.bindNewAccount')}
                </Button>
              </div>
              <Table
                columns={authAccountColumns}
                dataSource={authAccountData}
                pagination={false}
                size="middle"
                loading={loading || authProcessing}
                rowKey="email"
              />
            </div>

            {/* 授权店铺部分 */}
            <div>
              <h3 className="text-lg font-medium">{t('authManagement.authorizedStores')}</h3>
              <Table
                columns={authStoreColumns}
                dataSource={authStoreData}
                rowKey="locationId"
                pagination={{
                  current: storePagination.current,
                  pageSize: storePagination.pageSize,
                  total: storePagination.total,
                  showSizeChanger: false,
                  showQuickJumper: false,
                  showTotal: (total, range) =>
                    t('common.paginationTotal', {
                      start: range[0],
                      end: range[1],
                      total
                    })
                }}
                loading={storeLoading || authProcessing}
                onChange={handleStoreTableChange}
                size="middle"
              />
            </div>
          </div>
        )
      }
    ],
    [activeTab, hasAbnormalAccount, authAccountData, authStoreData, storePagination, storeLoading, loading, authProcessing]
  );

  return (
    <>
      <Card>
        <Tabs activeKey={activeTab} onChange={key => setActiveTab(key as PlatformType)} items={tabItems} size="large" />
      </Card>

      {/* 重新绑定确认弹窗 */}
      <Modal
        open={rebindConfirmModalVisible}
        onCancel={() => setRebindConfirmModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setRebindConfirmModalVisible(false)}>
            {t('common.cancel')}
          </Button>,
          <Button key="confirm" type="primary" onClick={handleRebindConfirm}>
            {t('authManagement.rebind')}
          </Button>
        ]}
        width={420}
        centered
        closeIcon={<span style={{ fontSize: '20px', color: '#666' }}>×</span>}
      >
        <div className="py-4">
          <h3 className="text-lg font-medium mb-4">{t('authManagement.rebindNewAccount')}</h3>
          <p className="text-gray-600 leading-relaxed">{t('authManagement.rebindAccountDescription')}</p>
        </div>
      </Modal>

      {/* 绑定新账户弹窗 */}
      <Modal
        title={t('authManagement.bindNewAccountTitle')}
        open={bindNewAccountModalVisible}
        onCancel={handleBindNewAccountCancel}
        footer={null}
        width={370}
        centered
        closeIcon={<span style={{ fontSize: '20px', color: '#666' }}>×</span>}
      >
        <div className="py-6 text-center">
          <h2 className="text-2xl font-medium mb-8 text-gray-800">{t('authManagement.oneClickBind')}</h2>
          <Button
            type="primary"
            size="large"
            block
            onClick={handleGoogleLogin}
            disabled={authProcessing}
            loading={authProcessing}
            style={{
              backgroundColor: '#1677ff',
              borderColor: '#1677ff',
              height: '48px',
              fontSize: '16px',
              fontWeight: '500'
            }}
          >
            {t('authManagement.useGoogleAccountLogin')}
          </Button>
        </div>
      </Modal>

      {/* 解绑确认弹窗 */}
      <Modal
        title={
          <div className="flex items-center gap-6">
            <img src={WarningIcon} alt="warning" className="w-6 h-6" />
            {t('authManagement.unbindAccount')}
          </div>
        }
        open={unbindModalVisible}
        onCancel={handleUnbindCancel}
        footer={[
          <Button key="cancel" onClick={handleUnbindCancel}>
            {t('common.cancel')}
          </Button>,
          <Button key="confirm" type="primary" onClick={handleUnbindConfirm}>
            {t('authManagement.unbind')}
          </Button>
        ]}
        width={520}
        centered
      >
        <div className="py-4">
          <p className="text-gray-600 leading-relaxed">{t('authManagement.unbindAccountDescription')}</p>
        </div>
      </Modal>

      {/* 商户选择Modal */}
      <Modal
        title={<div className="font-[20px] leading-[26px] font-medium">{t('authManagement.authorizingGoogleAccount')}</div>}
        open={merchantSelectionModalVisible}
        onCancel={handleMerchantSelectionCancel}
        footer={[
          <Button key="cancel" onClick={handleMerchantSelectionCancel}>
            {t('common.cancel')}
          </Button>,
          <Button key="confirm" type="primary" onClick={handleMerchantSelectionConfirm} loading={merchantSelectionLoading}>
            {t('authManagement.bindNewAccount')}
          </Button>
        ]}
        width={445}
        centered
        closeIcon={<span style={{ fontSize: '20px', color: '#666' }}>×</span>}
      >
        <div className="py-4">
          {/* Google账号显示 */}
          <div className="mb-6">
            <div
              className="flex h-10 items-center gap-3 p-3 border rounded-lg bg-gray-50 w-fit rounded-[100px]"
              style={{ border: '1px solid rgba(0, 0, 0, 0.25)' }}
            >
              <div className="w-8 h-8 bg-[#1E6812] rounded-full flex items-center justify-center text-white font-medium">
                {currentGoogleAccountEmail.charAt(0).toUpperCase()}
              </div>
              <div>
                <div className="font-medium">{currentGoogleAccountEmail}</div>
              </div>
            </div>
          </div>

          {/* 店铺选择说明 */}
          <div className="mb-4">
            <h3 className="text-lg font-medium mb-2">{t('authManagement.selectStoreToBind')}</h3>
          </div>

          {/* 全选选项 - 仅在Agent模式下显示 */}
          {!isMerchant && allGoogleLocations?.length > 0 && (
            <div className="mb-4">
              <Checkbox checked={selectAll} onChange={e => handleSelectAll(e.target.checked)} disabled={merchantSelectionLoading}>
                <span className="text-blue-600 font-medium">{t('authManagement.selectAll')}</span>
              </Checkbox>
            </div>
          )}

          {/* 店铺列表 */}
          <div className="max-h-60 overflow-y-auto">
            <Space direction="vertical" size="small" className="w-full">
              {merchantSelectionLoading ? (
                <div className="text-center py-8">
                  <div>{t('common.loading')}</div>
                </div>
              ) : (
                // 对店铺列表进行排序：已绑定的排在前面
                allGoogleLocations
                  .sort((a, b) => {
                    const aIsBound = boundGoogleLocations.some(bound => bound.locationId.toString() === a.locationId);
                    const bIsBound = boundGoogleLocations.some(bound => bound.locationId.toString() === b.locationId);

                    // 已绑定的排在前面
                    if (aIsBound && !bIsBound) return -1;
                    if (!aIsBound && bIsBound) return 1;
                    return 0;
                  })
                  .map(location => {
                    const isSelected = selectedLocationIds.includes(location.locationId);
                    const isBound = boundGoogleLocations.some(bound => bound.locationId.toString() === location.locationId);

                    return (
                      <div key={location.locationId} className="flex items-center justify-start gap-2">
                        {isMerchant ? (
                          // 商户模式：单选 Radio
                          <Radio
                            name="merchantLocation"
                            checked={isSelected}
                            onChange={e => handleLocationSelect(location.locationId, e.target.checked)}
                          />
                        ) : (
                          // Agent模式：多选 Checkbox
                          <Checkbox
                            checked={isSelected}
                            disabled={isBound}
                            onChange={e => handleLocationSelect(location.locationId, e.target.checked)}
                          />
                        )}
                        <div className="flex-1">
                          <span className="text-[#1D2129] leading-[22px]">{location.title ?? '--'}</span>
                        </div>
                      </div>
                    );
                  })
              )}
            </Space>
          </div>
        </div>
      </Modal>
    </>
  );
};
