import { Modal, List, Badge, Spin, message } from 'antd';
import { useState, useEffect, useRef } from 'react';
import { getSystemMessages, markSystemMessageAsRead } from '@/api/user';
import type { SystemMessage } from '@/types/api';
import MailBox from '@/assets/svg/mailbox.svg';
import MessageEmpty from '@/assets/svg/message-empty.svg';
import { TextEllipsis } from '@/components/TextEllipsis';
import { useTranslation } from 'react-i18next';

interface MessageModalProps {
  visible: boolean;
  onClose: () => void;
  onBadgeUpdate?: () => void; // 用于更新badge状态
}

export const MessageModal = ({ visible, onClose, onBadgeUpdate }: MessageModalProps) => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<SystemMessage[]>([]);
  const [selectedMessage, setSelectedMessage] = useState<SystemMessage | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pagination, setPagination] = useState({
    pageNum: 0,
    pageSize: 20,
    totalElements: 0
  });
  const listRef = useRef<HTMLDivElement>(null);

  // 检查是否还有更多数据
  const hasMore = messages.length < pagination.totalElements;

  // 加载系统消息
  const loadMessages = async (pageNum: number = 0, isLoadMore: boolean = false) => {
    if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await getSystemMessages({ pageNum, pageSize: pagination.pageSize });
      if (response) {
        if (isLoadMore) {
          // 加载更多时追加数据
          setMessages(prev => [...prev, ...response.content]);
        } else {
          // 首次加载时替换数据
          setMessages(response.content);
          setSelectedMessage(response.content[0]);
        }

        setPagination(prev => ({
          ...prev,
          pageNum,
          totalElements: response.totalElements
        }));
      }
    } catch (error) {
      message.error(t('common.loadMessagesFailed'));
      console.error('Failed to load messages:', error);
    } finally {
      if (isLoadMore) {
        setLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  // 加载更多数据
  const loadMore = async () => {
    if (loadingMore || !hasMore) return;

    const nextPageNum = pagination.pageNum + 1;
    await loadMessages(nextPageNum, true);
  };

  // 滚动事件处理
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const threshold = 100; // 距离底部100px时触发加载

    if (scrollHeight - scrollTop - clientHeight < threshold && hasMore && !loadingMore) {
      loadMore();
    }
  };

  useEffect(() => {
    if (visible) {
      // 重置状态
      setMessages([]);
      setSelectedMessage(null);
      setPagination(prev => ({ ...prev, pageNum: 0, totalElements: 0 }));
      loadMessages(0, false);
    }
  }, [visible]);

  const handleMessageClick = async (selectedMsg: SystemMessage) => {
    setSelectedMessage(selectedMsg);

    // 如果消息未读，标记为已读
    if (!selectedMsg.readFlag) {
      try {
        await markSystemMessageAsRead({ id: selectedMsg.id });
        // 更新本地状态
        setMessages(prev => prev.map(msg => (msg.id === selectedMsg.id ? { ...msg, readFlag: true } : msg)));
        // 通知Navbar更新badge
        onBadgeUpdate?.();
      } catch (error) {
        console.error('Failed to mark message as read:', error);
      }
    }
  };

  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-full w-full">
      <img src={MessageEmpty} alt="message-icon" className="w-20 h-20" />
      <div
        className=" mt-10 text-base"
        style={{
          color: 'rgba(0, 0, 0, 0.88)'
        }}
      >
        {t('common.noMessages')}
      </div>
    </div>
  );

  const renderMessageList = () => (
    <div ref={listRef} className="h-full overflow-y-auto" onScroll={handleScroll}>
      <List
        dataSource={messages}
        renderItem={msg => (
          <List.Item
            onClick={() => handleMessageClick(msg)}
            style={{
              padding: '16px 20px ',
              marginBottom: 16,
              border: '1px solid #D9D9D940',
              borderRadius: 10,
              backgroundColor: selectedMessage?.id === msg.id ? '#F7F8FA' : 'white'
            }}
          >
            <div className="flex items-start gap-4 w-full">
              <img src={MailBox} alt="message-icon" className="w-10 h-10" />
              <div className="flex-1 min-w-0">
                <div className="flex items-center mb-1">
                  <span className="font-medium text-gray-900">{msg.title}</span>
                  {!msg.readFlag && <Badge dot />}
                </div>
                <TextEllipsis text={msg.content} width="100%" />
              </div>
            </div>
          </List.Item>
        )}
      />

      {/* 加载更多指示器 */}
      {loadingMore && (
        <div className="flex items-center justify-center py-4">
          <Spin size="small" />
          <span className="ml-2 text-gray-500">{t('common.loadMore')}</span>
        </div>
      )}

      {/* 没有更多数据提示 */}
      {!hasMore && messages.length > 0 && (
        <div className="flex items-center justify-center py-4">
          <span className="text-gray-400 text-sm">{t('common.noMoreMessages')}</span>
        </div>
      )}
    </div>
  );

  const renderMessageDetail = () => {
    if (!selectedMessage) {
      return null;
    }

    return (
      <>
        <div className="flex items-center justify-between w-full">
          <h3 className="text-base font-medium ">{selectedMessage.title}</h3>
          <span className="text-[#999999]">{selectedMessage.createTime}</span>
        </div>
        <div className="flex-1 overflow-y-auto">
          <div className="whitespace-pre-wrap">{selectedMessage.content}</div>
        </div>
      </>
    );
  };

  return (
    <Modal
      title={
        <div className="flex justify-between items-center">
          <span className="text-xl font-medium">{t('navigation.messageCenter')}</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={880}
      centered
      destroyOnClose
      closable={true}
      bodyStyle={{ height: '550px', padding: '0' }}
    >
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <Spin size="large" />
        </div>
      ) : messages.length === 0 ? (
        renderEmptyState()
      ) : (
        <section className="flex h-full w-full flex-row justify-start items-start">
          {/* 左侧消息列表 */}
          <div className="flex h-full">
            <div className="w-80">{renderMessageList()}</div>
          </div>
          {/* 右侧消息详情 */}
          <div className="flex-1 flex flex-col justify-start items-start p-6 bg-white gap-4" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
            {renderMessageDetail()}
          </div>
        </section>
      )}
    </Modal>
  );
};
