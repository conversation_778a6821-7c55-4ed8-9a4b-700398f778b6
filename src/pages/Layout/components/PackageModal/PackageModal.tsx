import { Mo<PERSON>, Button } from 'antd';
import { useState, useEffect } from 'react';
import { getUserPackageInfo } from '@/api/user';
import type { UserPackageInfoResponse } from '@/types/api';
import PackageAvatar from '@/assets/svg/package-avatar.svg';
import PackageIcon from '@/assets/svg/package-icon.svg';
import ErrorFill from '@/assets/svg/error-fill.svg';
import { formatDateText } from '@/utils/utils';
import './index.module.css';
import { useTranslation } from 'react-i18next';

interface PackageModalProps {
  visible: boolean;
  onClose: () => void;
}

export const PackageModal = ({ visible, onClose }: PackageModalProps) => {
  const { t } = useTranslation();
  const [packageInfo, setPackageInfo] = useState<UserPackageInfoResponse | null>(null);
  const [loading, setLoading] = useState(false);

  // 获取用户套餐信息
  useEffect(() => {
    if (visible) {
      setLoading(true);
      getUserPackageInfo()
        .then(data => {
          setPackageInfo(data);
        })
        .catch(error => {
          console.error('Failed to fetch package info:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible]);

  const handleUpgrade = () => {
    // TODO: 实现升级套餐逻辑
    console.log('升级套餐');
  };

  const isExpiringSoon = packageInfo && packageInfo.daysLeft <= 30;

  return (
    <Modal
      title={
        <div
          className="flex justify-between items-center pb-4 px-6 "
          style={{
            borderBottom: '1px solid rgba(5, 5, 5, 0.06)',
            margin: '-4px -24px 0 -24px'
          }}
        >
          <span className="text-xl font-medium text-[#1F1F1F]">{t('navigation.package')}</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      height={460}
      centered
      closable={true}
    >
      {loading ? (
        <div className="text-center py-12">
          <div>{t('common.loading')}</div>
        </div>
      ) : packageInfo ? (
        <div className="flex h-full p-1">
          {/* 左侧：用户信息 */}
          <div
            className="flex flex-col items-center justify-between rounded-[20px] h-[388px] p-6 "
            style={{ width: '400px', border: '1px solid rgba(5, 5, 5, 0.06)' }}
          >
            {/* 用户头像和基本信息 */}
            <div className="flex flex-col items-center gap-4" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
              <div className="rounded-full flex items-center justify-center">
                <img src={PackageAvatar} alt="package" className="w-[120px] h-[120px]" />
              </div>
              <div className="flex flex-col items-center ">
                <div className="text-xl font-medium ">{packageInfo.username}</div>
                <div>{t('package.platformAccountLogin')}</div>
              </div>

              {/* 套餐版本信息 */}
              <div className="flex items-center gap-1">
                <img src={PackageIcon} alt="icon" className="h-[15px]" />
                <div className="text-xl  text-[#966D1B]">
                  {packageInfo.packageName}
                  {t('package.expiresIn', { date: formatDateText(packageInfo.endTime) })}
                </div>
              </div>
            </div>

            {/* 过期提醒 */}
            {isExpiringSoon && (
              <div className="flex items-center mb-6">
                <img src={ErrorFill} alt="error" className="w-[16px] h-[16px] mr-2" />
                <span className="text-red-500">{t('package.daysLeft', { days: packageInfo.daysLeft })}</span>
              </div>
            )}

            {/* 升级套餐按钮 */}
            <Button
              type="primary"
              onClick={handleUpgrade}
              disabled={true}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.25)',
                borderColor: 'white',
                height: '40px',
                fontSize: '12px',
                width: '200px',
                padding: '7px 16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '4px'
              }}
            >
              {t('package.upgradeNow')}
            </Button>
          </div>

          {/* 右侧：账户权益 */}
          <div className="flex-1 pl-10  text-[#000000D9]">
            <div className="flex flex-col">
              <div className="text-start mb-4">{t('package.currentAccountBenefits')}</div>

              <div className="flex justify-start items-center flex-1 bg-[#F7F8FA] px-10 py-4">
                {/* 店铺数量 */}
                <div className="flex flex-col justify-start items-start gap-2 mr-[60px]">
                  <div>{t('package.storeCount')}</div>
                  <div className="text-xl font-600">
                    {/* -1 代表专业版，无限制 */}
                    {packageInfo.merchantCount}/{Number(packageInfo.maxMerchantCount) === -1 ? '无限制' : packageInfo.maxMerchantCount}
                  </div>
                </div>

                {/* 账户数量 */}
                <div className="flex flex-col justify-start items-start gap-2">
                  <div>{t('package.accountCount')}</div>
                  <div className="text-xl font-600">{packageInfo.accountCount}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <div>{t('package.noPackageInfo')}</div>
        </div>
      )}
    </Modal>
  );
};
