import { useNavigate } from 'react-router-dom';
import { useCallback } from 'react';
import { Button } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useAuth } from '@/contexts/AuthContext';
import { useMerchant } from '@/contexts/MerchantContext';
import { useTranslation } from 'react-i18next';

interface MerchantLayoutWrapperProps {
  children: React.ReactNode;
}

export const MerchantLayoutWrapper: React.FC<MerchantLayoutWrapperProps> = ({ children }) => {
  const { t } = useTranslation();
  const { clearRole } = useAuth();
  const { merchant } = useMerchant();
  const navigate = useNavigate();

  const handleBackToHome = useCallback(async () => {
    await clearRole();
    setTimeout(() => {
      navigate('/', { replace: true });
    }, 100);
  }, [clearRole, navigate]);

  return (
    <div>
      {/* 面包屑导航 */}
      <div
        style={{
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <Button
          type="text"
          icon={<ArrowLeftOutlined style={{ fontSize: '14px', color: '#666' }} />}
          onClick={handleBackToHome}
          style={{
            height: 'auto',
            fontSize: '14px',
            color: '#666',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}
        >
          {t('common.back')}
        </Button>
        <div className="flex items-center justify-start items-center gap-1 text-[#666]">
          <span className="cursor-pointer" onClick={handleBackToHome}>
            Agent
          </span>
          <span>/</span>
          <span className="cursor-pointer">{merchant?.title || t('common.merchant')}</span>
        </div>
      </div>

      {/* 商户页面内容 */}
      {children}
    </div>
  );
};
