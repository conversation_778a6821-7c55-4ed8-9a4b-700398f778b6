import { ReactNode } from 'react';
import { MenuCodeEnums, RouteConfig } from '@/components/components';
import { useRouteGuard } from '@/hooks/useRouteGuard';
import { useAuth } from '@/contexts/AuthContext';

interface GuardedRouteProps {
  children: ReactNode;
  requiredMenuCode?: MenuCodeEnums;
  allRoutes: RouteConfig[];
}

export const GuardedRoute = ({ children, requiredMenuCode, allRoutes }: GuardedRouteProps) => {
  const { loading } = useAuth();
  const { isAuthorized } = useRouteGuard(allRoutes, requiredMenuCode);

  if (loading) {
    return children; // 让 Layout 显示骨架屏
  }

  return isAuthorized ? children : null;
};
