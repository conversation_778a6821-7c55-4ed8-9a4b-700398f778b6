import { useAuth } from '@/contexts/AuthContext';
import { MerchantLayoutWrapper } from './MerchantLayoutWrapper';
import { useMemo } from 'react';

interface DynamicPageWrapperProps {
  agentComponent: React.ComponentType;
  merchantComponent: React.ComponentType;
}

export const DynamicPageWrapper: React.FC<DynamicPageWrapperProps> = ({
  agentComponent: AgentComponent,
  merchantComponent: MerchantComponent
}) => {
  const { merchantId, agentId, loading } = useAuth();

  // 使用 useMemo 来避免不必要的重渲染
  const componentToRender = useMemo(() => {
    // 如果还在加载中，返回 null，让上层的 Layout 显示骨架屏
    if (loading) {
      return null;
    }

    // 判断是否应该显示商户页面
    const shouldShowMerchant = Boolean(merchantId);

    if (shouldShowMerchant) {
      if (agentId) {
        return (
          <MerchantLayoutWrapper>
            <MerchantComponent />
          </MerchantLayoutWrapper>
        );
      } else {
        // 商户用户
        return <MerchantComponent />;
      }
    } else {
      // Agent用户，显示Agent组件
      return <AgentComponent />;
    }
  }, [merchantId, agentId, loading, AgentComponent, MerchantComponent]);

  return componentToRender;
};
