import { useAuth } from '@/contexts/AuthContext';
import { MerchantLayoutWrapper } from './MerchantLayoutWrapper';
import { useMemo } from 'react';
import { Skeleton } from 'antd';

interface DynamicPageWrapperProps {
  agentComponent: React.ComponentType;
  merchantComponent: React.ComponentType;
}

// 简化的加载组件
const LoadingComponent = () => (
  <div className="bg-white/80 rounded-lg p-6">
    <Skeleton active avatar paragraph={{ rows: 6 }} />
  </div>
);

export const DynamicPageWrapper: React.FC<DynamicPageWrapperProps> = ({
  agentComponent: AgentComponent,
  merchantComponent: MerchantComponent
}) => {
  const { merchantId, agentId, loading } = useAuth();

  // 使用 useMemo 来避免不必要的重渲染，并且添加更稳定的判断逻辑
  const componentToRender = useMemo(() => {
    // 如果还在加载中，显示加载组件而不是 null
    if (loading) {
      return <LoadingComponent />;
    }

    // 判断是否应该显示商户页面
    const shouldShowMerchant = Boolean(merchantId);

    if (shouldShowMerchant) {
      if (agentId) {
        return (
          <MerchantLayoutWrapper>
            <MerchantComponent />
          </MerchantLayoutWrapper>
        );
      } else {
        // 商户用户
        return <MerchantComponent />;
      }
    } else {
      // Agent用户，显示Agent组件
      return <AgentComponent />;
    }
  }, [merchantId, agentId, loading, AgentComponent, MerchantComponent]);

  return componentToRender;
};
