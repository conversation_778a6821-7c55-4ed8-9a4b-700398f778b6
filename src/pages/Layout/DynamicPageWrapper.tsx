import { useAuth } from '@/contexts/AuthContext';
import { MerchantLayoutWrapper } from './MerchantLayoutWrapper';

interface DynamicPageWrapperProps {
  agentComponent: React.ComponentType;
  merchantComponent: React.ComponentType;
}

export const DynamicPageWrapper: React.FC<DynamicPageWrapperProps> = ({
  agentComponent: AgentComponent,
  merchantComponent: MerchantComponent
}) => {
  const { merchantId, agentId } = useAuth();

  // 判断是否应该显示商户页面
  const shouldShowMerchant = Boolean(merchantId);

  if (shouldShowMerchant) {
    if (agentId) {
      return (
        <MerchantLayoutWrapper>
          <MerchantComponent />
        </MerchantLayoutWrapper>
      );
    } else {
      // 商户用户
      return <MerchantComponent />;
    }
  } else {
    // Agent用户，显示Agent组件
    return <AgentComponent />;
  }
};
