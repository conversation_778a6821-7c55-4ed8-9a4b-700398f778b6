import { Outlet } from 'react-router-dom';
import { Layout as AntLayout } from 'antd';
import Navbar from './Navbar.tsx';
import { BaseSider } from '../../components/Sider/BaseSider.tsx';
import background from '@/assets/background.png';

const { Content } = AntLayout;

const Layout = () => {
  // 移除这里的 loading 检查，让 DynamicPageWrapper 处理加载状态
  // 这样可以避免整个布局的闪烁
  return (
    <AntLayout
      className="min-h-screen dan-social-web"
      style={{ backgroundImage: `url(${background})`, backgroundSize: 'cover', backgroundPosition: 'center' }}
    >
      <Navbar />
      <AntLayout style={{ height: 'calc(100vh - 112px)' }}>
        <BaseSider />
        <Content className="m-4" style={{ height: '100%', overflow: 'scroll' }}>
          <Outlet />
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
