import { Outlet } from 'react-router-dom';
import { Layout as AntLayout, Skeleton } from 'antd';
import Navbar from './Navbar.tsx';
import { BaseSider } from '../../components/Sider/BaseSider.tsx';
import background from '@/assets/background.png';
import { useAuth } from '@/contexts/AuthContext';

const { Content } = AntLayout;

// 简化的骨架屏 Loading 页面
const LoadingPage = () => (
  <div className="bg-white/80 min-h-screen rounded-lg p-6">
    <Skeleton active avatar paragraph={{ rows: 10 }} />
  </div>
);

const Layout = () => {
  const { loading } = useAuth();
  // 在Agent Layout中确保角色为默认AGENT权限

  if (loading) {
    return <LoadingPage />;
  }

  return (
    <AntLayout
      className="min-h-screen dan-social-web"
      style={{ backgroundImage: `url(${background})`, backgroundSize: 'cover', backgroundPosition: 'center' }}
    >
      <Navbar />
      <AntLayout style={{ height: 'calc(100vh - 112px)' }}>
        <BaseSider />
        <Content className="m-4" style={{ height: '100%', overflow: 'scroll' }}>
          <Outlet />
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
