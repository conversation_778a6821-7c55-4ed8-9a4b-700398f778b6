import { Layout, Menu, Dropdown, Space, Badge } from 'antd';
import logo from '@/assets/logo.png';
import { useCallback, useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { PackageModal } from '@/pages/Layout/components/PackageModal/PackageModal';
import { useAuth } from '@/contexts/AuthContext';
import { MessageModal } from '@/pages/Layout/components/MessageCenter/MessageModal';
import { getSystemMessageBadge } from '@/api/user';
import PackageSvg from '@/assets/svg/nav-package.svg';
import MessageSvg from '@/assets/svg/nav-message.svg';
import LogoutSvg from '@/assets/svg/nav-logout.svg';
import AvatarIcon from '@/assets/svg/avatar.svg?react';
import DropdownIcon from '@/assets/svg/dropdown.svg?react';
import { useTranslation } from 'react-i18next';
import { LanguageSwitcher } from '@/components/BusinessComponents/LanguageSwitch';

const { Header } = Layout;

const Navbar = () => {
  const { currentUser, logout, merchantId } = useAuth();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [packageModalVisible, setPackageModalVisible] = useState(false);
  const [messageModalVisible, setMessageModalVisible] = useState(false);
  const [hasReadAllMessage, setHasReadAllMessage] = useState(false);
  const isMerchant = useMemo(() => merchantId, [merchantId]);

  const title = useMemo(() => {
    if (isMerchant) {
      return t('navigation.welcome', { username: currentUser?.username });
    }
    return t('navigation.agentPortal');
  }, [currentUser?.merchantId, currentUser?.agentId, currentUser?.username, t]);

  const handleLogout = useCallback(() => {
    logout();
    // 将当前语言作为参数传递到登录页面
    const currentLanguage = i18n.language;
    // { replace: true }
    navigate(`/login?lang=${currentLanguage}`);
  }, [logout, navigate, i18n.language]);

  const handleOpenPackageModal = useCallback(() => {
    setPackageModalVisible(true);
  }, []);

  const handleClosePackageModal = useCallback(() => {
    setPackageModalVisible(false);
  }, []);

  const handleOpenMessageModal = useCallback(() => {
    setMessageModalVisible(true);
  }, []);

  const handleCloseMessageModal = useCallback(() => {
    setMessageModalVisible(false);
  }, []);

  // 检查未读消息badge
  const checkMessageBadge = useCallback(async () => {
    const hasReadAllMessage = await getSystemMessageBadge();
    setHasReadAllMessage(hasReadAllMessage);
  }, []);

  // 更新badge状态的回调
  const handleBadgeUpdate = useCallback(() => {
    checkMessageBadge();
  }, [checkMessageBadge]);

  // 组件加载时检查badge
  useEffect(() => {
    if (currentUser) {
      checkMessageBadge();
    }
  }, [currentUser, checkMessageBadge]);

  const userMenu = (
    <Menu>
      <Menu.Item
        key="package"
        icon={<img src={PackageSvg} alt="package" />}
        onClick={handleOpenPackageModal}
        className="flex items-center justify-start w-fit"
      >
        {t('navigation.package')}
      </Menu.Item>
      <Menu.Item
        key="messages"
        icon={<img src={MessageSvg} alt="message" />}
        onClick={handleOpenMessageModal}
        className="flex items-center justify-start w-fit"
      >
        <div className="flex items-center justify-between w-full">
          <span>{t('navigation.messageCenter')}</span>
          {!hasReadAllMessage && <Badge dot />}
        </div>
      </Menu.Item>
      <Menu.Item
        key="logout"
        icon={<img src={LogoutSvg} alt="logout" />}
        onClick={handleLogout}
        className="flex items-center justify-start"
      >
        {t('navigation.logout')}
      </Menu.Item>
    </Menu>
  );

  return (
    <>
      <Header
        className={`flex justify-between items-center px-6 py-3 ${isMerchant ? 'bg-[#B6354C]' : 'bg-transparent'}`}
        style={{ height: 112 }}
      >
        <img src={logo} alt="logo" style={{ width: 100 }} />
        <div className="flex flex-col items-center justify-start gap-1 h-[65px]">
          <div className="text-white font-semibold text-[32px] h-[42px] leading-[42px] text-center font-fang-yuan">{title}</div>
          <div className="text-white font-weight-400 text-14px text-center -mt-6 h-[18px]">{t('navigation.systemTitle')}</div>
        </div>
        <div className="flex items-center gap-[10px]">
          {currentUser && (
            <Dropdown overlay={userMenu} placement="bottomRight" overlayClassName="user-dropdown-menu">
              <Space className={`flex items-center cursor-pointer ${isMerchant ? 'text-white' : ''}`} size="small">
                <span className="mr-2">{t('navigation.hello', { username: currentUser.username || t('navigation.userName') })}</span>
                <AvatarIcon className={`!w-5 !h-5 ${isMerchant ? 'text-white' : 'opacity-65'}`} />
                <DropdownIcon className={`ml-2 ${isMerchant ? 'text-white' : 'opacity-65'}`} />
              </Space>
            </Dropdown>
          )}
          <LanguageSwitcher style={{ color: isMerchant ? 'white' : 'rgba(0, 0, 0, 0.88)', marginLeft: -10 }} hideIcon />
        </div>
      </Header>

      <PackageModal visible={packageModalVisible} onClose={handleClosePackageModal} />
      <MessageModal visible={messageModalVisible} onClose={handleCloseMessageModal} onBadgeUpdate={handleBadgeUpdate} />
    </>
  );
};

export default Navbar;
