import React, { useState, useEffect, useCallback } from 'react';
import { Dropdown, Button } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { useTranslation } from 'react-i18next';
import DropdownIcon from '@/assets/svg/dropdown.svg?react';
import { getMerchantSatisfactionRanking } from '@/api/merchant';
import type { MerchantRankingData } from '@/types';
import { SectionContainer } from './SectionContainer';

interface CustomerSatisfactionRankingProps {
  agentId: number;
  userId: number;
}

export const CustomerSatisfactionRanking: React.FC<CustomerSatisfactionRankingProps> = ({ agentId }) => {
  const { t } = useTranslation();
  const [sortOrder, setSortOrder] = useState<'HIGH_TO_LOW' | 'LOW_TO_HIGH'>('HIGH_TO_LOW'); // 默认从高到低
  const [timeRange, setTimeRange] = useState<'RECENT_90_DAYS' | 'ALL_TIME'>('RECENT_90_DAYS');
  const [data, setData] = useState<MerchantRankingData[]>([]);

  // 获取排名数据
  const fetchRankingData = useCallback(async () => {
    const response = await getMerchantSatisfactionRanking({
      agentId,
      sortOrder,
      timeRange
    });
    if (response?.rankings) {
      setData(response.rankings);
    }
  }, [agentId, sortOrder, timeRange]);

  // 初始加载和参数变化时重新获取数据
  useEffect(() => {
    fetchRankingData();
  }, [fetchRankingData]);

  // 生成 ECharts 配置
  const getChartOption = () => {
    const categories = data.map(item => item.merchantName);
    const values = data.map(item => item.satisfactionScore);

    return {
      grid: {
        left: '5%',
        right: '5%',
        top: '3%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        min: 1.0,
        max: 5.0,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          color: '#666',
          fontSize: 12
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            type: 'solid'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: categories,
        boundaryGap: true,
        inverse: true,
        // inverse: sortOrder === 'HIGH_TO_LOW',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666',
          fontSize: 14,
          margin: 16,
          width: 140,
          overflow: 'truncate',
          fontWeight: 'normal'
        }
      },
      series: [
        {
          type: 'bar',
          data: values,

          barCategoryGap: '10%',
          itemStyle: {
            color: '#0D728F',
            borderRadius: [0, 4, 4, 0]
          },
          label: {
            show: true,
            position: 'right',
            color: '#333',
            fontSize: 12,
            formatter: '{c}'
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}: ${data.value}`;
        }
      }
    };
  };

  // 排序选项
  const sortOrderItems = [
    {
      key: 'HIGH_TO_LOW',
      label: t('satisfaction.highToLow'),
      onClick: () => setSortOrder('HIGH_TO_LOW')
    },
    {
      key: 'LOW_TO_HIGH',
      label: t('satisfaction.lowToHigh'),
      onClick: () => setSortOrder('LOW_TO_HIGH')
    }
  ];

  // 时间范围选项
  const timeRangeItems = [
    {
      key: 'RECENT_90_DAYS',
      label: t('satisfaction.last90Days'),
      onClick: () => setTimeRange('RECENT_90_DAYS')
    },
    {
      key: 'ALL_TIME',
      label: t('satisfaction.allTime'),
      onClick: () => setTimeRange('ALL_TIME')
    }
  ];

  // 获取当前选中的标签
  const getSortOrderLabel = () => {
    return sortOrder === 'HIGH_TO_LOW' ? t('satisfaction.highToLow') : t('satisfaction.lowToHigh');
  };

  const getTimeRangeLabel = () => {
    return timeRange === 'RECENT_90_DAYS' ? t('satisfaction.last90Days') : t('satisfaction.allTime');
  };

  return (
    <SectionContainer title={t('satisfaction.customerSatisfactionRanking')}>
      {/* 标题和操作区域 */}
      <div className="flex gap-2">
        <Dropdown menu={{ items: sortOrderItems }} trigger={['click']}>
          <Button
            type="text"
            style={{
              color: '#0D728F',
              boxShadow: 'none',
              padding: '4px 8px'
            }}
          >
            {getSortOrderLabel()} <DropdownIcon />
          </Button>
        </Dropdown>
        <Dropdown menu={{ items: timeRangeItems }} trigger={['click']}>
          <Button
            type="text"
            style={{
              color: '#0D728F',
              boxShadow: 'none',
              padding: '4px 8px'
            }}
          >
            {getTimeRangeLabel()} <DropdownIcon />
          </Button>
        </Dropdown>
      </div>
      <ReactECharts option={getChartOption()} style={{ height: '100%', width: '100%' }} opts={{ renderer: 'svg' }} />
    </SectionContainer>
  );
};
