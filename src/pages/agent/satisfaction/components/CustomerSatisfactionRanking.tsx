import React, { useState, useMemo, useEffect } from 'react';
import { Card, Select, Spin, message } from 'antd';
import ReactECharts from 'echarts-for-react';
import { useTranslation } from 'react-i18next';
import { getMerchantSatisfactionRanking } from '@/api/merchant';
import type { MerchantRankingData } from '@/types';
import { SectionContainer } from './SectionContainer';

interface CustomerSatisfactionRankingProps {
  agentId: number;
  userId: number;
}

export const CustomerSatisfactionRanking: React.FC<CustomerSatisfactionRankingProps> = ({ agentId, userId }) => {
  const { t } = useTranslation();
  const [sortOrder, setSortOrder] = useState<'HIGH_TO_LOW' | 'LOW_TO_HIGH'>('HIGH_TO_LOW'); // 默认从高到低
  const [timeRange, setTimeRange] = useState<'RECENT_90_DAYS' | 'ALL_TIME'>('RECENT_90_DAYS');
  const [data, setData] = useState<MerchantRankingData[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取排名数据
  const fetchRankingData = async () => {
    setLoading(true);
    try {
      const response = await getMerchantSatisfactionRanking({
        agentId,
        // userId,
        sortOrder,
        timeRange
      });

      if (response?.rankings) {
        setData(response.rankings);
      }
    } catch (error) {
      console.error('Failed to fetch ranking data:', error);
      message.error(t('satisfaction.fetchDataError'));
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和参数变化时重新获取数据
  useEffect(() => {
    fetchRankingData();
  }, [agentId, userId, sortOrder, timeRange]);

  // 根据排序方式处理数据（API已经返回排序后的数据，这里主要用于显示）
  const sortedData = useMemo(() => {
    return data;
  }, [data]);

  // 生成 ECharts 配置
  const getChartOption = () => {
    const categories = sortedData.map(item => item.merchantName);
    const values = sortedData.map(item => item.satisfactionScore);

    return {
      grid: {
        left: '20%',
        right: '10%',
        top: '5%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        min: 4.0,
        max: 5.0,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          color: '#666',
          fontSize: 12
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            type: 'solid'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#333',
          fontSize: 12,
          margin: 10
        }
      },
      series: [
        {
          type: 'bar',
          data: values,
          barWidth: 20,
          itemStyle: {
            color: '#0D728F',
            borderRadius: [0, 4, 4, 0]
          },
          label: {
            show: true,
            position: 'right',
            color: '#333',
            fontSize: 12,
            formatter: '{c}'
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}: ${data.value}`;
        }
      }
    };
  };

  // <div className="flex justify-between items-start">
  //   <div>
  //     <div className="text-lg font-medium text-gray-800">{}</div>
  //     <div className="text-sm text-gray-500 mt-1">{t('satisfaction.last30DaysData')}</div>
  //     <div className="flex gap-2 mt-2">
  //       <Select
  //         value={sortOrder}
  //         onChange={setSortOrder}
  //         size="small"
  //         style={{ width: 140 }}
  //         options={[
  //           { value: 'HIGH_TO_LOW', label: t('satisfaction.highToLow') },
  //           { value: 'LOW_TO_HIGH', label: t('satisfaction.lowToHigh') }
  //         ]}
  //       />
  //     </div>
  //   </div>
  //   <Select
  //     value={timeRange}
  //     onChange={setTimeRange}
  //     size="small"
  //     style={{ width: 120 }}
  //     options={[
  //       { value: 'RECENT_90_DAYS', label: t('satisfaction.last90Days') },
  //       { value: 'ALL_TIME', label: t('satisfaction.allTime') }
  //     ]}
  //   />
  // </div>;

  return (
    <SectionContainer title={t('satisfaction.customerSatisfactionRanking')}>
      <div style={{ height: 400 }}>
        <ReactECharts option={getChartOption()} style={{ height: '100%', width: '100%' }} opts={{ renderer: 'svg' }} />
      </div>
    </SectionContainer>
  );
};
