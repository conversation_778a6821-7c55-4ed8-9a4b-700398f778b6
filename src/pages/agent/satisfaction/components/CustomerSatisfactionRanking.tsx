import React, { useState, useMemo } from 'react';
import { Card, Select } from 'antd';
import ReactECharts from 'echarts-for-react';
import { useTranslation } from 'react-i18next';

// Mock 数据类型
interface CustomerSatisfactionData {
  merchantName: string;
  satisfactionScore: number;
}

// Mock 数据
const mockSatisfactionData: CustomerSatisfactionData[] = [
  { merchantName: '新客', satisfactionScore: 4.8 },
  { merchantName: '回头客', satisfactionScore: 4.7 },
  { merchantName: 'Savory Spoon', satisfactionScore: 4.6 },
  { merchantName: 'Flavor Fusion', satisfactionScore: 4.5 },
  { merchantName: 'Culinary Canvas', satisfactionScore: 4.4 },
  { merchantName: 'The Green Fork', satisfactionScore: 4.3 },
  { merchantName: 'The Green Fork', satisfactionScore: 4.2 }
];

interface CustomerSatisfactionRankingProps {
  data?: CustomerSatisfactionData[];
}

export const CustomerSatisfactionRanking: React.FC<CustomerSatisfactionRankingProps> = ({ data = mockSatisfactionData }) => {
  const { t } = useTranslation();
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc'); // 默认从高到低
  const [timeRange, setTimeRange] = useState<string>('last30Days');

  // 根据排序方式处理数据
  const sortedData = useMemo(() => {
    const sorted = [...data].sort((a, b) => {
      if (sortOrder === 'desc') {
        return b.satisfactionScore - a.satisfactionScore; // 从高到低
      } else {
        return a.satisfactionScore - b.satisfactionScore; // 从低到高
      }
    });
    return sorted;
  }, [data, sortOrder]);

  // 生成 ECharts 配置
  const getChartOption = () => {
    const categories = sortedData.map(item => item.merchantName);
    const values = sortedData.map(item => item.satisfactionScore);

    return {
      grid: {
        left: '20%',
        right: '10%',
        top: '5%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        min: 4.0,
        max: 5.0,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          color: '#666',
          fontSize: 12
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            type: 'solid'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#333',
          fontSize: 12,
          margin: 10
        }
      },
      series: [
        {
          type: 'bar',
          data: values,
          barWidth: 20,
          itemStyle: {
            color: '#0D728F',
            borderRadius: [0, 4, 4, 0]
          },
          label: {
            show: true,
            position: 'right',
            color: '#333',
            fontSize: 12,
            formatter: '{c}'
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}: ${data.value}`;
        }
      }
    };
  };

  return (
    <Card
      title={
        <div className="flex justify-between items-start">
          <div>
            <div className="text-lg font-medium text-gray-800">{t('satisfaction.customerSatisfactionRanking')}</div>
            <div className="text-sm text-gray-500 mt-1">{t('satisfaction.last30DaysData')}</div>
            <div className="flex gap-2 mt-2">
              <Select
                value={sortOrder}
                onChange={setSortOrder}
                size="small"
                style={{ width: 140 }}
                options={[
                  { value: 'desc', label: t('satisfaction.highToLow') },
                  { value: 'asc', label: t('satisfaction.lowToHigh') }
                ]}
              />
            </div>
          </div>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            size="small"
            style={{ width: 120 }}
            options={[
              { value: 'last30Days', label: t('satisfaction.last30Days') },
              { value: 'last7Days', label: t('satisfaction.last7Days') },
              { value: 'last90Days', label: t('satisfaction.last90Days') },
              { value: 'all', label: '整体' }
            ]}
          />
        </div>
      }
      className="h-full"
    >
      <div style={{ height: 400 }}>
        <ReactECharts option={getChartOption()} style={{ height: '100%', width: '100%' }} opts={{ renderer: 'svg' }} />
      </div>
    </Card>
  );
};
