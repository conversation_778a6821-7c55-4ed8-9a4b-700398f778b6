import React, { useState, useMemo, useEffect } from 'react';
import { Spin, message, Dropdown, Button } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { useTranslation } from 'react-i18next';
import { getMerchantSatisfactionRanking } from '@/api/merchant';
import type { MerchantRankingData } from '@/types';
import { SectionContainer } from './SectionContainer';

interface CustomerSatisfactionRankingProps {
  agentId: number;
  userId: number;
}

export const CustomerSatisfactionRanking: React.FC<CustomerSatisfactionRankingProps> = ({ agentId, userId }) => {
  const { t } = useTranslation();
  const [sortOrder, setSortOrder] = useState<'HIGH_TO_LOW' | 'LOW_TO_HIGH'>('HIGH_TO_LOW'); // 默认从高到低
  const [timeRange, setTimeRange] = useState<'RECENT_90_DAYS' | 'ALL_TIME'>('RECENT_90_DAYS');
  const [data, setData] = useState<MerchantRankingData[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取排名数据
  const fetchRankingData = async () => {
    setLoading(true);
    try {
      const response = await getMerchantSatisfactionRanking({
        agentId,
        // userId,
        sortOrder,
        timeRange
      });

      if (response?.rankings) {
        setData(response.rankings);
      }
    } catch (error) {
      console.error('Failed to fetch ranking data:', error);
      message.error(t('satisfaction.fetchDataError'));
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和参数变化时重新获取数据
  useEffect(() => {
    fetchRankingData();
  }, [agentId, userId, sortOrder, timeRange]);

  // 根据排序方式处理数据（API已经返回排序后的数据，这里主要用于显示）
  const sortedData = useMemo(() => {
    return data;
  }, [data]);

  // 生成 ECharts 配置
  const getChartOption = () => {
    const categories = sortedData.map(item => item.merchantName);
    const values = sortedData.map(item => item.satisfactionScore);

    return {
      grid: {
        left: '15%',
        right: '5%',
        top: '3%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        min: 4.0,
        max: 5.0,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          color: '#666',
          fontSize: 12
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            type: 'solid'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#333',
          fontSize: 12,
          margin: 10
        }
      },
      series: [
        {
          type: 'bar',
          data: values,
          barWidth: 30,
          itemStyle: {
            color: '#0D728F',
            borderRadius: [0, 4, 4, 0]
          },
          label: {
            show: true,
            position: 'right',
            color: '#333',
            fontSize: 12,
            formatter: '{c}'
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}: ${data.value}`;
        }
      }
    };
  };

  // <div className="flex justify-between items-start">
  //   <div>
  //     <div className="text-lg font-medium text-gray-800">{}</div>
  //     <div className="text-sm text-gray-500 mt-1">{t('satisfaction.last30DaysData')}</div>
  //     <div className="flex gap-2 mt-2">
  //       <Select
  //         value={sortOrder}
  //         onChange={setSortOrder}
  //         size="small"
  //         style={{ width: 140 }}
  //         options={[
  //           { value: 'HIGH_TO_LOW', label: t('satisfaction.highToLow') },
  //           { value: 'LOW_TO_HIGH', label: t('satisfaction.lowToHigh') }
  //         ]}
  //       />
  //     </div>
  //   </div>
  //   <Select
  //     value={timeRange}
  //     onChange={setTimeRange}
  //     size="small"
  //     style={{ width: 120 }}
  //     options={[
  //       { value: 'RECENT_90_DAYS', label: t('satisfaction.last90Days') },
  //       { value: 'ALL_TIME', label: t('satisfaction.allTime') }
  //     ]}
  //   />
  // </div>;

  // 排序选项
  const sortOrderItems = [
    {
      key: 'HIGH_TO_LOW',
      label: t('satisfaction.highToLow'),
      onClick: () => setSortOrder('HIGH_TO_LOW')
    },
    {
      key: 'LOW_TO_HIGH',
      label: t('satisfaction.lowToHigh'),
      onClick: () => setSortOrder('LOW_TO_HIGH')
    }
  ];

  // 时间范围选项
  const timeRangeItems = [
    {
      key: 'RECENT_90_DAYS',
      label: t('satisfaction.last90Days'),
      onClick: () => setTimeRange('RECENT_90_DAYS')
    },
    {
      key: 'ALL_TIME',
      label: t('satisfaction.allTime'),
      onClick: () => setTimeRange('ALL_TIME')
    }
  ];

  // 获取当前选中的标签
  const getSortOrderLabel = () => {
    return sortOrder === 'HIGH_TO_LOW' ? t('satisfaction.highToLow') : t('satisfaction.lowToHigh');
  };

  const getTimeRangeLabel = () => {
    return timeRange === 'RECENT_90_DAYS' ? t('satisfaction.last90Days') : t('satisfaction.allTime');
  };

  return (
    <SectionContainer title={t('satisfaction.customerSatisfactionRanking')}>
      {/* 标题和操作区域 */}
      <div className="flex gap-2">
        <Dropdown menu={{ items: sortOrderItems }} trigger={['click']}>
          <Button
            type="text"
            style={{
              color: 'rgba(0, 0, 0, 0.88)',
              border: 'none',
              boxShadow: 'none',
              padding: '4px 8px'
            }}
          >
            {getSortOrderLabel()} <DownOutlined />
          </Button>
        </Dropdown>
        <Dropdown menu={{ items: timeRangeItems }} trigger={['click']}>
          <Button
            type="text"
            style={{
              color: 'rgba(0, 0, 0, 0.88)',
              border: 'none',
              boxShadow: 'none',
              padding: '4px 8px'
            }}
          >
            {getTimeRangeLabel()} <DownOutlined />
          </Button>
        </Dropdown>
      </div>

      {/* 图表区域 */}
      <div style={{ height: 500, width: '100%' }}>
        <Spin spinning={loading}>
          <ReactECharts option={getChartOption()} style={{ height: '100%', width: '100%' }} opts={{ renderer: 'svg' }} />
        </Spin>
      </div>
    </SectionContainer>
  );
};
