import React from 'react';
import { Table } from 'antd';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useMerchant } from '@/contexts/MerchantContext';
import styles from './MerchantActivityRanking.module.css';
import { SectionContainer } from './SectionContainer';
import { MerchantText } from '@/components/MerchantText';

// Mock 数据类型
interface MerchantActivityData {
  rank: number;
  merchantId: number;
  merchantName: string;
  platform: string;
  reviewCount: number;
}

// Mock 数据
const mockMerchantData: MerchantActivityData[] = [
  { rank: 1, merchantId: 1, merchantName: 'JUN bistro', platform: 'Google', reviewCount: 1000 },
  { rank: 2, merchantId: 2, merchantName: 'Savory Spoon', platform: 'Google', reviewCount: 900 },
  { rank: 3, merchantId: 3, merchantName: 'Flavor Fusion', platform: 'Yelp', reviewCount: 800 },
  { rank: 4, merchantId: 4, merchantName: 'Culinary Canvas', platform: 'Google', reviewCount: 700 },
  { rank: 5, merchantId: 5, merchantName: 'Taste Haven', platform: 'Google', reviewCount: 600 },
  { rank: 6, merchantId: 6, merchantName: 'The Green Fork', platform: 'Google', reviewCount: 500 },
  { rank: 7, merchantId: 7, merchantName: 'Bistro Bliss', platform: 'Google', reviewCount: 400 },
  { rank: 8, merchantId: 8, merchantName: 'Epicurean Escape', platform: 'Google', reviewCount: 300 },
  { rank: 9, merchantId: 9, merchantName: 'Gourmet Garden', platform: 'Google', reviewCount: 200 },
  { rank: 10, merchantId: 10, merchantName: 'Delightful Dishes', platform: 'Google', reviewCount: 100 }
];

interface MerchantActivityRankingProps {
  data?: MerchantActivityData[];
}

export const MerchantActivityRanking: React.FC<MerchantActivityRankingProps> = ({ data = mockMerchantData }) => {
  const { t } = useTranslation();

  const columns = [
    {
      title: '',
      dataIndex: 'rank',
      key: 'rank',
      width: 40,
      render: (rank: number) => (
        <div className="bg-[#E4F5F5] w-[24px] h-[24px] rounded-[24px] font-medium text-[#0D728F] leading-6 text-center">{rank}</div>
      )
    },
    {
      title: t('satisfaction.merchantName'),
      render: (record: MerchantActivityData) => (
        <MerchantText merchant={{ id: record?.merchantId, title: record?.merchantName }} routeJump={true} />
      )
    },
    {
      title: t('satisfaction.platform'),
      dataIndex: 'platform',
      key: 'platform',
      width: 80,
      render: (platform: string) => <span className="text-gray-600">{platform}</span>
    },
    {
      title: t('satisfaction.reviewCount'),
      dataIndex: 'reviewCount',
      key: 'reviewCount',
      width: 100,
      align: 'right' as const,
      render: (count: number) => (
        <span className="text-gray-800 font-medium">
          {count}
          {t('satisfaction.reviews')}
        </span>
      )
    }
  ];

  return (
    <SectionContainer title={t('satisfaction.merchantActivityRanking')}>
      <div className="w-full flex flex-row justify-between items-center" style={{ color: 'background:  rgba(0, 0, 0, 0.65)' }}>
        <span>{t('satisfaction.rankTopTenMerchants')}</span>
        <span>{t('satisfaction.last30DaysReviewData')}</span>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        size="small"
        rowKey="rank"
        className={`${styles.merchantActivityTable} w-full`}
        showHeader={false}
      />
    </SectionContainer>
  );
};
