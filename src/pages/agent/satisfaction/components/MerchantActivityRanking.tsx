import React from 'react';
import { Card, Table } from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './MerchantActivityRanking.module.css';

// Mock 数据类型
interface MerchantActivityData {
  rank: number;
  merchantName: string;
  platform: string;
  reviewCount: number;
}

// Mock 数据
const mockMerchantData: MerchantActivityData[] = [
  { rank: 1, merchantName: 'JUN bistro', platform: 'Google', reviewCount: 1000 },
  { rank: 2, merchantName: 'Savory Spoon', platform: 'Google', reviewCount: 900 },
  { rank: 3, merchantName: 'Flavor Fusion', platform: 'Yelp', reviewCount: 800 },
  { rank: 4, merchantName: 'Culinary Canvas', platform: 'Google', reviewCount: 700 },
  { rank: 5, merchantName: 'Taste Haven', platform: 'Google', reviewCount: 600 },
  { rank: 6, merchantName: 'The Green Fork', platform: 'Google', reviewCount: 500 },
  { rank: 7, merchantName: 'Bistro Bliss', platform: 'Google', reviewCount: 400 },
  { rank: 8, merchantName: 'Epicurean Escape', platform: 'Google', reviewCount: 300 },
  { rank: 9, merchantName: 'Gourmet Garden', platform: 'Google', reviewCount: 200 },
  { rank: 10, merchantName: 'Delightful Dishes', platform: 'Google', reviewCount: 100 }
];

interface MerchantActivityRankingProps {
  data?: MerchantActivityData[];
}

export const MerchantActivityRanking: React.FC<MerchantActivityRankingProps> = ({ data = mockMerchantData }) => {
  const { t } = useTranslation();

  const columns = [
    {
      title: '',
      dataIndex: 'rank',
      key: 'rank',
      width: 40,
      render: (rank: number) => <span className="text-gray-600 font-medium">{rank}</span>
    },
    {
      title: t('satisfaction.merchantName'),
      dataIndex: 'merchantName',
      key: 'merchantName',
      render: (name: string) => <span className="text-blue-600 font-medium cursor-pointer hover:text-blue-800">{name}</span>
    },
    {
      title: t('satisfaction.platform'),
      dataIndex: 'platform',
      key: 'platform',
      width: 80,
      render: (platform: string) => <span className="text-gray-600">{platform}</span>
    },
    {
      title: t('satisfaction.reviewCount'),
      dataIndex: 'reviewCount',
      key: 'reviewCount',
      width: 100,
      align: 'right' as const,
      render: (count: number) => (
        <span className="text-gray-800 font-medium">
          {count}
          {t('satisfaction.reviews')}
        </span>
      )
    }
  ];

  return (
    <Card
      title={
        <div>
          <div className="text-lg font-medium text-gray-800">{t('satisfaction.merchantActivityRanking')}</div>
          <div className="text-sm text-gray-500 mt-1">{t('satisfaction.last30DaysData')}</div>
        </div>
      }
      className="h-full"
    >
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        size="small"
        rowKey="rank"
        className={styles.merchantActivityTable}
        scroll={{ y: 400 }}
      />
    </Card>
  );
};
