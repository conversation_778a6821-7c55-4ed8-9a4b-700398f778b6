import { Row, Col } from 'antd';
import { MerchantActivityRanking, CustomerSatisfactionRanking } from './components';
import { useAuth } from '@/contexts/AuthContext';

const Satisfaction = () => {
  const { agentId, currentUser } = useAuth();

  // 确保有必要的用户信息
  if (!agentId || !currentUser?.userId) {
    return <div>Loading...</div>;
  }

  return (
    <Row gutter={[16, 16]}>
      <Col span={12}>
        <MerchantActivityRanking />
      </Col>
      <Col span={12}>
        <CustomerSatisfactionRanking agentId={agentId} userId={currentUser.userId} />
      </Col>
    </Row>
  );
};

export default Satisfaction;
