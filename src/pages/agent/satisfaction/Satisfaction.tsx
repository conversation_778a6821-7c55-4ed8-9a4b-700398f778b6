import '@/components/BusinessComponents/SatisfactionTabs/index.css';
import { SatisfactionTabs } from '@/components/BusinessComponents/SatisfactionTabs';
import { useCallback, useEffect, useState } from 'react';
import { RadarChartDataResponse } from '@/types/api';
import { getSatisfactionRadarChart } from '@/api/satisfaction';
import { useAuth } from '@/contexts/AuthContext';

const Satisfaction = () => {
  const [dataSource, setDataSource] = useState<RadarChartDataResponse>();
  const { userContext } = useAuth();

  const fetchData = useCallback(async () => {
    const data = await getSatisfactionRadarChart({
      agentId: userContext?.agentId
    });
    setDataSource(data);
  }, []);

  useEffect(() => {
    fetchData();
  }, []);

  return <SatisfactionTabs dataSource={dataSource} />;
};

export default Satisfaction;
