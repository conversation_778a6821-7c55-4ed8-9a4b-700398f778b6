import React, { useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import { <PERSON><PERSON>, Card } from 'antd';
import { MerchantTag } from '@/components/MerchantTag';
import { Merchant } from '@/types/merchant';
import { StatisticsResponse } from '@/types/dashboard';
import { getTriangleStyle } from '@/utils/utils';
import { getTrendColor } from '@/utils/utils';
import { useTranslation } from 'react-i18next';

interface StatisticsItem {
  label?: string;
  value: number;
  color: string;
  icon?: React.ReactNode;
}

interface BaseStatisticsProps {
  title: string;
  centerTitle: string;
  centerValue: number;
  changePercentage: number | string;
  onViewAll?: () => void;
  listTitle: string;
  listItems?: Merchant[];
  abnormalIcon?: string;
  normalColor?: string;
  abnormalColor?: string;
  data: StatisticsResponse;
  normalText?: string;
  abnormalText?: string;
}

export const BaseStatistics: React.FC<BaseStatisticsProps> = ({
  title,
  centerTitle,
  centerValue,
  changePercentage,
  onViewAll,
  listTitle,
  listItems = [],
  abnormalIcon,
  normalColor = '#1E6812',
  abnormalColor = '#CF9C70',
  normalText,
  abnormalText,
  data
}) => {
  const { t } = useTranslation();

  // 准备 ECharts 配置
  const items: StatisticsItem[] = useMemo(
    () => [
      {
        color: normalColor,
        value: data?.normal || 0,
        label: normalText || t('home.normalCount')
      },
      {
        color: abnormalColor,
        value: data?.abnormal || 0,
        label: abnormalText || t('home.abnormalCount')
      }
    ],
    [data, normalColor, abnormalColor, normalText, abnormalText]
  );

  const chartOption = useMemo(
    () => ({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: title,
          type: 'pie',
          radius: ['60%', '75%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            }
          },
          labelLine: {
            show: false
          },
          data: items.map(item => ({
            value: item.value,
            name: item.label,
            itemStyle: {
              color: item.color
            }
          }))
        }
      ]
    }),
    [items, title]
  );

  const isChangeValid = useMemo(() => {
    return changePercentage && changePercentage !== '--';
  }, [changePercentage]);

  return (
    <Card className="h-fit flex-1 min-w-0 h-[416px]" bodyStyle={{ padding: '20px' }}>
      {/* 标题 */}
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-800 m-0">{title}</h3>
      </div>

      {/* 图表和统计数据水平排列 */}
      <div className="flex items-center justify-between mb-6">
        {/* 图表容器 */}
        <div className="relative flex-shrink-0" style={{ height: '200px', width: '200px' }}>
          <ReactECharts option={chartOption} style={{ height: '100%', width: '100%' }} opts={{ renderer: 'canvas' }} />

          {/* 中心文字 */}
          <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
            <div className="text-sm font-medium -mb-3">{centerTitle}</div>
            <div className="text-[48px] font-bold flex items-center justify-center">{centerValue}</div>
            <div
              className="text-xs flex items-center flex-row justify-center gap-[2px] -mt-3"
              style={{ color: isChangeValid ? getTrendColor(Number(changePercentage)) : '' }}
            >
              <span>{isChangeValid ? Math.abs(Number(changePercentage)) : '--'}%</span>
              <span style={isChangeValid ? getTriangleStyle(Number(changePercentage)) : { display: 'none' }} />
              <span style={{ color: 'rgba(0, 0, 0, 0.65)' }}>{t('home.comparedToYesterday')}</span>
            </div>
          </div>
        </div>

        {/* 统计数据 */}
        <div className="flex flex-col ml-8 space-y-4">
          {items.map((item, index) => (
            <div key={index} className="flex items-center">
              <div className="w-2 h-2 mr-1 flex-shrink-0" style={{ backgroundColor: item.color }} />
              <span className="text-sm font-medium">{item.label}</span>
              <span className="text-sm font-medium ml-2">
                {item.value}
                <span className="font-normal">{t('home.count')}</span>
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="h-[108px]">
        {/* 列表部分 - 网格布局 */}

        <div>
          {/* listTitle 和 查看全部 */}
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center">
              <img src={abnormalIcon} alt="abnormal" className="w-8 h-8 mr-2" />
              <span className="text-base font-medium text-gray-700">{listTitle}</span>
            </div>
            {onViewAll && (
              <Button type="text" disabled={listItems?.length === 0} onClick={onViewAll} className="text-[#0D728F]  cursor-pointer p-0">
                {t('common.viewAll')}
              </Button>
            )}
          </div>
          <div className="flex flex-wrap justify-start items-start gap-[2px] h-[68px] overflow-y-hidden">
            {listItems?.length > 0 ? (
              listItems.map(item => <MerchantTag key={item.id} merchant={item} routeJump={true} />)
            ) : (
              <span className="m-auto" style={{ color: 'rgba(0, 0, 0, 0.25)' }}>
                {t('home.noAbnormalMerchants')}
              </span>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};
