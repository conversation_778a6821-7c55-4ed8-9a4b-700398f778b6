import { useState, useEffect, useMemo } from 'react';
import { DatePicker, Tabs } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import ReactECharts from 'echarts-for-react';
import { getCustomerSatisfaction } from '@/api/dashboard';
import { CustomerSatisfactionStatisticsRequest, CustomerSatisfactionStatisticsResponse } from '@/types/dashboard';
import { useAuth } from '@/contexts/AuthContext';
import { getTrendColor } from '@/utils/utils';
import { useTranslation } from 'react-i18next';
import './index.css';
import { PeriodType } from '@/types/review';

const { RangePicker } = DatePicker;

const getDefaultRange = (type: string): [Dayjs, Dayjs] => {
  if (type === 'week') return [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')];
  if (type === 'month') return [dayjs().subtract(29, 'day').startOf('day'), dayjs().endOf('day')];
  if (type === 'quarter') return [dayjs().subtract(89, 'day').startOf('day'), dayjs().endOf('day')];
  return [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')];
};

export const MerchantSatisfactionStatistics = () => {
  const { t } = useTranslation();
  const timeOptions = [
    { label: t('time.last7Days'), value: 'week' },
    { label: t('time.last30Days'), value: 'month' },
    { label: t('time.last90Days'), value: 'quarter' }
  ];
  const [tabKey, setTabKey] = useState<string>('week');
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>(getDefaultRange('week'));
  const [data, setData] = useState<CustomerSatisfactionStatisticsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const { agentId } = useAuth();

  const handleTabChange = (key: string) => {
    setTabKey(key);
    const range = getDefaultRange(key);
    setDateRange(range);
  };

  const handleRangeChange = (dates: any) => {
    if (dates) {
      setDateRange(dates);
      setTabKey('');
    }
  };

  // 生成完整的日期数组并填充数据
  const chartData = useMemo(() => {
    if (!data?.dailyStatistics || !dateRange) {
      return {
        dates: [],
        positiveReviews: [],
        negativeReviews: [],
        satisfactionPercentages: [],
        satisfactionRange: { min: 0, max: 100 }
      };
    }

    const [startDate, endDate] = dateRange;
    const dates: string[] = [];
    const positiveReviews: number[] = [];
    const negativeReviews: number[] = [];
    const satisfactionPercentages: number[] = [];

    // 创建数据映射表，方便查找
    const dataMap = new Map();
    data.dailyStatistics.forEach(item => {
      const dateKey = dayjs(item.date).format('YYYY-MM-DD');
      dataMap.set(dateKey, item);
    });

    // 生成完整的日期范围
    let currentDate = startDate.clone();
    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
      const dateKey = currentDate.format('YYYY-MM-DD');
      const dateDisplay = currentDate.format('M/D');

      dates.push(dateDisplay);

      // 检查是否有该日期的数据
      const dayData = dataMap.get(dateKey);
      if (dayData) {
        positiveReviews.push(dayData.positiveReviews);
        negativeReviews.push(dayData.negativeReviews);
        satisfactionPercentages.push(dayData.satisfactionPercentage || 0);
      } else {
        // 没有数据的日期填充为0
        positiveReviews.push(0);
        negativeReviews.push(0);
        satisfactionPercentages.push(0);
      }

      currentDate = currentDate.add(1, 'day');
    }

    // 动态计算满意度数据的范围
    const validSatisfactionData = satisfactionPercentages.filter(val => val !== 0);
    let satisfactionRange = { min: 0, max: 100 };

    if (validSatisfactionData.length > 0) {
      const minVal = Math.min(...validSatisfactionData);
      const maxVal = Math.max(...validSatisfactionData);

      // 给范围添加一些padding，让图表更美观
      const padding = Math.abs(maxVal - minVal) * 0.1 || 10;
      satisfactionRange = {
        min: Math.floor(minVal - padding),
        max: Math.ceil(maxVal + padding)
      };
    }

    return {
      dates,
      positiveReviews,
      negativeReviews,
      satisfactionPercentages,
      satisfactionRange
    };
  }, [data, dateRange]);

  // 动态计算图表宽度
  const chartStyle = useMemo(() => {
    const dataLength = chartData.dates.length;
    return dataLength > 7
      ? {
          width: '100%',
          height: 300
        }
      : {
          height: 300
        };
  }, [chartData.dates.length]);

  useEffect(() => {
    const fetchData = async () => {
      if (!dateRange) return;

      setLoading(true);
      try {
        const [start, end] = dateRange;
        let params: CustomerSatisfactionStatisticsRequest = {
          agentId,
          periodType: 'CUSTOM'
        };
        if (tabKey) {
          let periodTypeParam: PeriodType = PeriodType.PAST_7_DAYS;
          if (tabKey === 'month') {
            periodTypeParam = PeriodType.PAST_30_DAYS;
          } else if (tabKey === 'quarter') {
            periodTypeParam = PeriodType.PAST_90_DAYS;
          }
          params = {
            ...params,
            periodType: periodTypeParam
          };
        } else {
          params = {
            ...params,
            startTime: start.startOf('day').format('YYYY-MM-DDTHH:mm:ss'),
            endTime: end.endOf('day').format('YYYY-MM-DDTHH:mm:ss')
          };
        }
        const res = await getCustomerSatisfaction(params);
        setData(res);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [dateRange, agentId, tabKey]);

  // 柱状图配置
  const chartOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#4DB632',
          type: 'dashed',
          width: 1
        }
      },
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`;

        // 找到满意度数据
        const satisfactionParam = params.find((p: any) => p.seriesName === t('home.satisfaction'));
        const satisfactionValue = satisfactionParam ? satisfactionParam.value : 0;
        const sign = Number(satisfactionValue) >= 0 ? '+' : '';
        const color = getTrendColor(satisfactionValue);

        params.forEach((param: any) => {
          if (param.seriesName === t('home.positiveReviews')) {
            // 好评显示数值+百分比趋势
            result += `${param.marker}${param.seriesName}: ${param.value} <span style="color: ${color};">${sign}${satisfactionValue}%</span><br/>`;
          } else if (param.seriesName === t('home.negativeReviews')) {
            // 差评正常显示
            result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
          }
          // 跳过满意度单独显示
        });

        return result;
      }
    },
    legend: {
      data: [t('home.positiveReviews'), t('home.negativeReviews')],
      top: 0,
      left: 'center',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 14,
        color: 'rgba(0, 0, 0, 0.88)'
      }
    },
    grid: {
      left: '8%', // 从0%改为8%，为Y轴标签留出空间
      right: '8%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.dates,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(0, 0, 0, 0.65)',
        fontSize: 12
      }
    },
    yAxis: [
      {
        type: 'value',
        name: t('home.quantity'),
        min: 0,
        max: (value: any) => {
          // 以10为基础向上取整，确保最小值为10
          return Math.max(Math.ceil(value.max / 10) * 10, 10);
        },
        position: 'left',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(0, 0, 0, 0.65)',
          fontSize: 12
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1,
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        min: chartData.satisfactionRange.min,
        max: chartData.satisfactionRange.max,
        position: 'right',
        axisLine: {
          show: false // 隐藏轴线
        },
        axisTick: {
          show: false // 隐藏刻度线
        },
        axisLabel: {
          show: false // 隐藏标签
        },
        splitLine: {
          show: false // 隐藏分割线
        }
      }
    ],
    series: [
      {
        name: t('home.positiveReviews'),
        type: 'bar',
        data: chartData.positiveReviews,
        itemStyle: {
          color: '#1E6812',
          borderRadius: [2, 2, 0, 0]
        },
        barWidth: '20%',
        barGap: '10%'
      },
      {
        name: t('home.negativeReviews'),
        type: 'bar',
        data: chartData.negativeReviews,
        itemStyle: {
          color: '#F0C1AF',
          borderRadius: [2, 2, 0, 0]
        },
        barWidth: '20%'
      },
      // 满意度折线
      {
        name: t('home.satisfaction'),
        type: 'line',
        yAxisIndex: 1,
        data: chartData.satisfactionPercentages,
        lineStyle: {
          color: '#4DB632',
          width: 2
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#4DB632',
          borderColor: '#4DB632',
          borderWidth: 2
        },
        emphasis: {
          focus: 'series',
          lineStyle: {
            width: 3
          },
          itemStyle: {
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: '#4DB632'
          }
        },
        z: 10
      }
    ]
  };

  return (
    <div style={{ background: '#fff', borderRadius: 8, padding: 24 }}>
      {/* 标题和操作区 */}
      <div className="flex flex-row justify-between items-center mb-6">
        <div style={{ fontSize: 22, fontWeight: 600 }}>{t('home.customerSatisfactionStatistics')}</div>
        <div className="flex flex-row justify-end items-center gap-4">
          <Tabs
            className="home-satisfaction-tabs"
            activeKey={tabKey}
            onChange={handleTabChange}
            items={timeOptions.map(opt => ({ key: opt.value, label: opt.label }))}
            size="small"
            type="card"
          />
          <RangePicker value={dateRange} onChange={handleRangeChange} allowClear={false} />
        </div>
      </div>

      {/* 内容区 横向布局 */}
      {/* <div className="flex flex-row items-start gap-6 overflow-hidden"> */}
      {/* 左侧柱状图 - 自适应宽度 */}
      <div className="flex-1 min-w-0 overflow-auto max-w-full ">
        <ReactECharts option={chartOption} style={chartStyle} opts={{ renderer: 'canvas' }} showLoading={loading} />
      </div>

      {/* 右侧统计信息 - 固定宽度 */}
      {/* <div style={{ width: '300px', flexShrink: 0 }}>
          <div className="mb-6">
            <div className="font-500" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
              {t('home.statusAbnormalMerchants')}
            </div>
            <div className="flex flex-row items-end justify-start gap-3">
              <div className="text-[40px] font-bold text-[#000000]">{data?.abnormal || 0}</div>
            </div>
          </div> */}

      {/* 异常商户列表
          {data?.abnormalMerchant && data.abnormalMerchant.length > 0 && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center">
                  <img src={AbnormalIcon} alt="abnormal" className="w-8 h-8 mr-2" />
                  <span className="text-base font-medium text-gray-700">{t('home.abnormalMerchants')}</span>
                </div>
              </div>

              {/* 商户标签网格 */}
      {/* <div className="grid grid-cols-2 gap-2">
                {data.abnormalMerchant
                  ?.slice(0, 14)
                  ?.map((merchant, index) => <MerchantTag key={`${merchant.id}-${index}`} merchant={merchant} routeJump={true} />)}
              </div>
            </div>
          )}  */}
      {/* </div> */}
      {/* </div> */}
    </div>
  );
};
