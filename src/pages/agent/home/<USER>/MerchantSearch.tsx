import { useState, useEffect, useCallback } from 'react';
import { Select, Button, Row, Col } from 'antd';
import { useMerchant } from '@/contexts/MerchantContext';
import { getAllMerchants, getMerchantHistory, saveMerchantHistory } from '@/api/merchant';
import type { Merchant } from '@/types/merchant';
import { MerchantTag } from '@/components/MerchantTag';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';

export const MerchantSearch = () => {
  const { setMerchant } = useMerchant();
  const { switchRole, userContext } = useAuth();
  const { t } = useTranslation();
  const [selectedMerchantId, setSelectedMerchantId] = useState<number>();
  const [searchOptions, setSearchOptions] = useState<Merchant[]>([]);
  const [loading, setLoading] = useState(false);
  const [historyMerchants, setHistoryMerchants] = useState<Merchant[]>([]);
  const navigate = useNavigate();

  // 从API加载历史记录
  const loadHistoryMerchants = useCallback(async () => {
    try {
      const historyData = await getMerchantHistory();
      setHistoryMerchants(historyData);
    } catch (error) {
      console.error('Failed to load merchant history:', error);
      setHistoryMerchants([]);
    }
  }, []);

  // 初始化商户数据
  const initializeMerchants = useCallback(async () => {
    setLoading(true);
    try {
      const merchants = await getAllMerchants();
      setSearchOptions(merchants);
    } finally {
      setLoading(false);
    }
  }, []);

  // 搜索商户
  const handleSearch = useCallback(async (value: string) => {
    if (!value.trim()) {
      // 没有搜索词时显示所有选项
      await initializeMerchants();
      return;
    }

    setLoading(true);
    try {
      const merchants = await getAllMerchants(value.trim());
      setSearchOptions(merchants);
    } finally {
      setLoading(false);
    }
  }, []);

  // 处理选择商户
  const handleSelectMerchant = useCallback((merchantId: number) => {
    setSelectedMerchantId(merchantId);
  }, []);

  // 处理进入商户
  const handleEnterMerchant = useCallback(async () => {
    if (!selectedMerchantId) return;

    const selectedMerchant = searchOptions.find(merchant => merchant.id === selectedMerchantId);
    if (selectedMerchant) {
      // 转换API响应格式为前端使用的Merchant格式
      const merchant: Merchant = {
        id: selectedMerchant.id,
        title: selectedMerchant.title
      };

      // 设置商户信息到context
      setMerchant(merchant);

      // 调用API保存到历史记录
      await saveMerchantHistory(selectedMerchantId);
      if (userContext?.merchantRoleId) {
        await switchRole(userContext?.merchantRoleId, selectedMerchantId);
      }
      setTimeout(() => {
        navigate('/', { replace: true });
      }, 100);
    }
  }, [selectedMerchantId, searchOptions, setMerchant, userContext]);

  // 处理点击历史商户
  const handleClickHistoryMerchant = useCallback(
    async (merchantId: string) => {
      if (!merchantId) return;

      // 如果点击历史商户，也需要设置merchant context
      const historyMerchant = historyMerchants.find(merchant => merchant.id.toString() === merchantId);
      if (historyMerchant) {
        // 设置商户信息到context
        setMerchant(historyMerchant);

        await saveMerchantHistory(historyMerchant.id);
        if (userContext?.merchantRoleId) {
          await switchRole(userContext?.merchantRoleId, historyMerchant.id);
        }
        // 跳转到首页，DynamicPageWrapper 会自动显示 MerchantHome
        setTimeout(() => {
          navigate('/', { replace: true });
        }, 100);
      }
    },
    [historyMerchants, setMerchant, navigate, loadHistoryMerchants, userContext]
  );

  // 清空选择时的处理
  const handleClear = useCallback(() => {
    setSelectedMerchantId(undefined);
  }, []);

  useEffect(() => {
    // 初始化时加载历史记录和商户数据
    loadHistoryMerchants();
    initializeMerchants();
  }, [loadHistoryMerchants, initializeMerchants]);

  return (
    <div
      style={{
        background: 'white',
        padding: '20px',
        borderRadius: '6px'
      }}
    >
      {/* 搜索区域 */}
      <Row align="middle" className="mb-4">
        <Col flex="auto" style={{ marginRight: '10px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span className="text-base text-[#333] mr-[10px] w-fit">{t('home.merchantName')}</span>
            <Select
              showSearch
              placeholder={t('home.selectMerchant')}
              style={{ flex: 1 }}
              filterOption={false}
              loading={loading}
              onSearch={handleSearch}
              onSelect={handleSelectMerchant}
              onClear={handleClear}
              value={selectedMerchantId}
              allowClear
              notFoundContent={loading ? t('common.loading') : t('common.noData')}
              suffixIcon={
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M3 4.5L6 7.5L9 4.5" stroke="#999" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              }
              options={searchOptions
                .map(merchant => ({
                  label: merchant.title,
                  value: merchant.id
                }))
                ?.sort((a, b) => a.label.localeCompare(b.label))}
            />
          </div>
        </Col>
        <Col>
          <Button type="primary" onClick={handleEnterMerchant} disabled={!selectedMerchantId}>
            {t('home.enterMerchant')}
          </Button>
        </Col>
      </Row>

      {/* 历史商户网格 */}
      <div className="w-full flex flex-wrap gap-1">
        {historyMerchants?.map((merchant, index) => (
          <MerchantTag
            key={`${merchant.id}-${index}`}
            merchant={merchant}
            onClick={() => handleClickHistoryMerchant(merchant.id.toString())}
          />
        ))}
      </div>
    </div>
  );
};
