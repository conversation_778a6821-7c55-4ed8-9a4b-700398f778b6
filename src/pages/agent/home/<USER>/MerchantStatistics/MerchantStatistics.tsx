import React from 'react';
import { BaseStatistics } from '../BaseStatistics/BaseStatistics';
import { StatisticsLoading } from '../StatisticsLoading';
import { useMerchantStatistics } from '@/hooks/useDashboardData';
import MerchantAbnormal from '@/assets/svg/merchant-abnormal.svg';
import { useTranslation } from 'react-i18next';

export const MerchantStatistics: React.FC = () => {
  const { t } = useTranslation();
  const { data, loading } = useMerchantStatistics();

  if (loading) {
    return <StatisticsLoading />;
  }

  if (!data) {
    return null;
  }

  const handleViewAll = () => {
    // 后续实现跳转功能
  };

  return (
    <BaseStatistics
      title={t('home.merchantStatistics')}
      centerTitle={t('home.merchantManagementTotal')}
      centerValue={data.total}
      changePercentage={data.comparedToYesterday}
      onViewAll={handleViewAll}
      listTitle={t('home.abnormalMerchants')}
      listItems={data.abnormalMerchant}
      abnormalIcon={MerchantAbnormal}
      abnormalColor="#CF9C70"
      normalColor="#1E6812"
      data={data}
    />
  );
};
