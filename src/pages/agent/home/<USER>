import { MerchantSearch } from './components/MerchantSearch';
import { ReviewStatistics } from './components/ReviewStatistics';
import { MerchantStatistics } from './components/MerchantStatistics';
import { MerchantReviewsStatistics } from '@/components/BusinessComponents/MerchantReviewsStatistics';
import { MerchantSatisfactionStatistics } from './components/MerchantSatisfactionStatistics';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { PeriodType } from '@/types/review';

export const Home = () => {
  const { t } = useTranslation();

  useEffect(() => {
    if (window.location.pathname === '/') {
      // 先多 push 一次，确保有 forward
      window.history.pushState(null, '', window.location.href);
      window.history.pushState(null, '', window.location.href);
      const blockBack = () => {
        window.history.pushState(null, '', window.location.href);
      };
      window.addEventListener('popstate', blockBack);

      return () => {
        window.removeEventListener('popstate', blockBack);
      };
    }
  }, []);

  return (
    <section className="flex flex-col flex-start align-start gap-4" style={{ background: '#f0f2f5', minHeight: '100vh' }}>
      <section className="flex flex-row flex-start align-center gap-2 w-full">
        <MerchantStatistics />
        <ReviewStatistics />
      </section>
      <MerchantSearch />
      <MerchantReviewsStatistics
        title={t('home.merchantReviewsStatistics')}
        subTitle={t('home.totalReviews')}
        compareText={t('home.compared')}
        timeChange={true}
        defaultRange={PeriodType.PAST_30_DAYS}
      />
      <MerchantSatisfactionStatistics />
    </section>
  );
};
