import React from 'react';
import { BaseStatistics } from '../BaseStatistics/BaseStatistics';
import { StatisticsLoading } from '../StatisticsLoading';
import { useTodayComments } from '@/hooks/useDashboardData';
import ReviewsAbnormal from '@/assets/svg/reviews-abnormal.svg';
import { useTranslation } from 'react-i18next';

export const ReviewStatistics: React.FC = () => {
  const { t } = useTranslation();
  const { data, loading } = useTodayComments();

  if (loading) {
    return <StatisticsLoading />;
  }

  if (!data) {
    return null;
  }

  const handleViewAll = () => {
    // 后续实现跳转功能
    console.log(t('home.viewAllReviewStatistics'));
  };

  return (
    <BaseStatistics
      title={t('home.todayReviews')}
      centerTitle={t('home.todayReviewTotal')}
      centerValue={data.total}
      changePercentage={data.comparedToYesterday}
      onViewAll={handleViewAll}
      listTitle={t('home.todayNegativeReviewMerchants')}
      listItems={data.abnormalMerchant}
      abnormalIcon={ReviewsAbnormal}
      data={data}
      normalColor="#0D728F"
      abnormalColor="#E39A80"
      normalText={t('home.positiveReviewCount')}
      abnormalText={t('home.negativeReviewCount')}
    />
  );
};
