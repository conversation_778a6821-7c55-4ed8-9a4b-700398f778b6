import { useState, useCallback, useEffect } from 'react';
import { Card, Select, Button, Modal, Input, message, Alert } from 'antd';
import { getSystemTones, getMerchantToneSetting, setMerchantTone, submitFeedback } from '@/api/merchant';
import { useAuth } from '@/contexts/AuthContext';
import { ToneOption, MerchantSetToneRequest, FeedbackRequest, FeedbackType } from '@/types/api';
import { useTranslation } from 'react-i18next';
import './index.css';

const { TextArea } = Input;
const { Option } = Select;

export const MerchantSettings = () => {
  const { merchantId, currentUser } = useAuth();
  const { t } = useTranslation();
  const [selectedTone, setSelectedTone] = useState<number>();
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);
  const [feedbackContent, setFeedbackContent] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [toneSaving, setToneSaving] = useState(false);
  const [toneOptions, setToneOptions] = useState<ToneOption[]>([]);
  const [loading, setLoading] = useState(true);

  // 初始化数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        setLoading(true);

        // 获取系统语气列表
        const tonesResponse = await getSystemTones();
        if (tonesResponse?.tonnes) {
          setToneOptions(tonesResponse.tonnes);
        }

        // 获取商户当前语气设置
        if (merchantId) {
          const merchantToneResponse = await getMerchantToneSetting(merchantId);
          if (merchantToneResponse?.tonnes && merchantToneResponse.tonnes.length > 0) {
            setSelectedTone(merchantToneResponse.tonnes[0].code);
          }
        }
      } catch (error) {
        console.error('Failed to initialize data:', error);
        // 静默处理错误，保持默认值
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, [merchantId]);

  // 打开反馈弹窗
  const handleOpenFeedback = useCallback(() => {
    setFeedbackModalVisible(true);
  }, []);

  // 关闭反馈弹窗
  const handleCloseFeedback = useCallback(() => {
    setFeedbackModalVisible(false);
    setFeedbackContent('');
  }, []);

  // 提交反馈
  const handleSubmitFeedback = useCallback(async () => {
    if (!feedbackContent.trim()) {
      message.warning(t('settings.pleaseEnterFeedbackContent'));
      return;
    }

    if (!currentUser?.userId || !merchantId) {
      message.error(t('settings.userOrMerchantInfoFailed'));
      return;
    }

    setSubmitting(true);
    try {
      const feedbackData: FeedbackRequest = {
        userId: currentUser.userId,
        content: feedbackContent.trim(),
        feedbackType: FeedbackType.PRODUCT,
        merchantId: merchantId
      };

      await submitFeedback(feedbackData);
      message.success(t('settings.feedbackSubmittedSuccessfully'));
      handleCloseFeedback();
    } finally {
      setSubmitting(false);
    }
  }, [feedbackContent, currentUser, merchantId, handleCloseFeedback]);

  // 保存语气设置
  const handleToneChange = useCallback(
    async (value: number) => {
      if (!merchantId) {
        message.error(t('settings.merchantInfoFailed'));
        return;
      }

      setToneSaving(true);
      try {
        const request: MerchantSetToneRequest = {
          merchantId: merchantId,
          toneCode: value,
          description: `设置语气为: ${toneOptions.find(option => option.code === value)?.name || ''}`
        };

        await setMerchantTone(request);
        setSelectedTone(value);
        message.success(t('settings.settingsSaved'));
      } finally {
        setToneSaving(false);
      }
    },
    [merchantId, toneOptions, t]
  );

  return (
    <div className="bg-white/80 min-h-screen rounded-lg p-6">
      <div className="max-w-4xl">
        <h1 className="text-2xl font-bold mb-6" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
          {t('settings.personalizationSettings')}
        </h1>

        {/* 个性化建议提示卡片 */}
        <Alert
          className="mb-6"
          message={t('settings.morePersonalizationSuggestions')}
          showIcon={true}
          action={
            <Button type="primary" size="small" className="setting-feedback-btn" onClick={handleOpenFeedback}>
              {t('settings.feedbackMore')}
            </Button>
          }
        />

        {/* 语气设置 */}
        <Card className="mb-6">
          <div className="mb-4">
            <label className="text-base font-medium mb-3 block" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
              {t('settings.selectReplyTone')}
            </label>
            <Select
              value={selectedTone}
              onChange={handleToneChange}
              style={{ width: 200 }}
              size="large"
              loading={toneSaving || loading}
              placeholder={t('settings.selectTone')}
            >
              {toneOptions.map(option => (
                <Option key={option.code} value={option.code}>
                  {option.name}
                </Option>
              ))}
            </Select>
          </div>
        </Card>
      </div>

      {/* 反馈弹窗 */}
      <Modal
        title={<div className="font-medium">{t('settings.productFeedback')}</div>}
        open={feedbackModalVisible}
        onCancel={handleCloseFeedback}
        footer={null}
        width={800}
        centered
        closable={true}
        destroyOnClose
      >
        <div className="flex flex-col gap-6 justify-start items-start mt-6 w-full">
          <p className="text-base" style={{ color: 'rgba(0, 0, 0, 0.65)' }}>
            {t('settings.thankYouForValuableFeedback')}
          </p>

          <div className="w-full">
            <TextArea
              placeholder={t('settings.enterPersonalizationRequirements')}
              value={feedbackContent}
              onChange={e => setFeedbackContent(e.target.value)}
              rows={12}
              style={{
                width: '100%',
                minHeight: '300px',
                fontSize: '14px',
                lineHeight: '1.5'
              }}
            />
          </div>

          <div className="flex justify-end gap-3 w-full">
            <Button
              size="large"
              onClick={handleCloseFeedback}
              style={{
                height: '40px',
                padding: '0 24px',
                backgroundColor: '#F7F8FA',
                border: 'none'
              }}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="primary"
              size="large"
              loading={submitting}
              onClick={handleSubmitFeedback}
              style={{
                backgroundColor: '#0D728F',
                borderColor: '#0D728F',
                height: '40px',
                padding: '0 24px'
              }}
            >
              {t('common.submit')}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
