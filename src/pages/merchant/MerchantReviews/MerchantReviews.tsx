import { useState, useEffect, useCallback } from 'react';
import { Card, List, Typography, Spin, Tabs, Pagination } from 'antd';
import { PlatformEnums, GetGoogleReviewsResponseContent, StarRating } from '@/types/review';
import { SingleReview } from '@/components/BusinessComponents/SingleReview';
import { getGoogleReviews } from '@/api/review';
import { useMerchant } from '@/contexts/MerchantContext';
import { getMerchantLocations } from '@/api/review';
import { getStarRatingValue } from '@/utils/utils';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;
const { TabPane } = Tabs;

interface Platform {
  key: PlatformEnums;
  label: string;
  id?: number;
}

// 平台配置
const allPlatforms: Partial<Record<PlatformEnums, Platform>> = {
  [PlatformEnums.Google]: {
    key: PlatformEnums.Google,
    label: 'Google'
  },
  [PlatformEnums.Xiaohongshu]: {
    key: PlatformEnums.Xiaohongshu,
    label: '小红书'
  },
  [PlatformEnums.Yelp]: {
    key: PlatformEnums.Yelp,
    label: 'Yelp'
  },
  [PlatformEnums.Facebook]: {
    key: PlatformEnums.Facebook,
    label: 'Facebook'
  },
  [PlatformEnums.IG]: {
    key: PlatformEnums.IG,
    label: 'IG'
  },
  [PlatformEnums.TikTok]: {
    key: PlatformEnums.TikTok,
    label: 'TikTok'
  }
};

export const MerchantReviews = () => {
  const { t } = useTranslation();
  const { merchant } = useMerchant();
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [reviewList, setReviewList] = useState<GetGoogleReviewsResponseContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<Platform | null>(null);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  const onTabChange = useCallback(
    (key: string) => {
      setActiveTab(platforms.find(platform => platform.key === key) || null);
    },
    [platforms]
  );

  useEffect(() => {
    if (!merchant?.id) return;
    getMerchantLocations(merchant.id).then(res => {
      const newPlatforms =
        res?.map(item => ({
          ...item,
          ...allPlatforms[item.type]
        })) || [];
      setPlatforms(newPlatforms as Platform[]);
      if (newPlatforms.length > 0) setActiveTab(newPlatforms[0] as Platform);
    });
  }, [merchant?.id]);

  // 获取评论数据
  const fetchReviews = useCallback(async () => {
    try {
      setLoading(true);
      if (activeTab?.id) {
        const response = await getGoogleReviews({
          merchantLocationId: activeTab.id,
          pageNum: pageNum - 1,
          pageSize
        });
        if (response) {
          setReviewList(response?.content || []);
          setTotal(response?.totalElements || 0);
        }
      }
    } finally {
      setLoading(false);
    }
  }, [activeTab, pageNum, pageSize]);

  useEffect(() => {
    fetchReviews();
  }, [fetchReviews]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-4 bg-white min-h-screen w-[75%]">
      {/* 平台切换Tabs */}
      <Tabs activeKey={activeTab?.key} onChange={onTabChange} type="card" size="large">
        {platforms.map(platform => (
          <TabPane tab={<span className="w-[120px] flex items-center justify-center">{platform.label}</span>} key={platform.key} />
        ))}
      </Tabs>

      {/* 评论列表 */}
      {reviewList.length === 0 ? (
        <Card>
          <div className="text-center py-8">
            <Text type="secondary">{t('common.noData')}</Text>
          </div>
        </Card>
      ) : (
        <>
          <List
            itemLayout="vertical"
            dataSource={reviewList}
            renderItem={review => (
              <Card hoverable className="mb-4" key={review.id}>
                <SingleReview
                  review={{
                    ...review,
                    starRating: getStarRatingValue(review.starRating as StarRating)
                  }}
                  refresh={fetchReviews}
                />
              </Card>
            )}
          />

          <div className="flex justify-end">
            <Pagination
              className="mt-4"
              total={total}
              pageSize={pageSize}
              current={pageNum}
              onChange={(page, size) => {
                setPageNum(page);
                setPageSize(size);
              }}
            />
          </div>
        </>
      )}
    </div>
  );
};
