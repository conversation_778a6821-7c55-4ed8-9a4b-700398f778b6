import { getSatisfactionRadarChart } from '@/api/satisfaction';
import { SatisfactionTabs } from '@/components/BusinessComponents/SatisfactionTabs';
import { useMerchant } from '@/contexts/MerchantContext';
import { RadarChartDataResponse } from '@/types/api';
import { useCallback, useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export const MerchantSatisfaction = () => {
  const [dataSource, setDataSource] = useState<RadarChartDataResponse>();
  const { merchant } = useMerchant();
  const { userContext } = useAuth();

  const fetchData = useCallback(async () => {
    const data = await getSatisfactionRadarChart({
      merchantId: merchant?.id,
      agentId: userContext?.agentId
    });
    setDataSource(data);
  }, []);

  useEffect(() => {
    fetchData();
  }, []);

  return <SatisfactionTabs dataSource={dataSource} />;
};
