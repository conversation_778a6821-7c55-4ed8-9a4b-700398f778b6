import React from 'react';
import ReactECharts from 'echarts-for-react';
import { useTranslation } from 'react-i18next';

interface SatisfactionTrendChartProps {
  data?: Array<{
    date: string;
    value: number;
  }>;
  width?: number | string;
  height?: number | string;
  showGrid?: boolean;
}

export const SatisfactionTrendChart: React.FC<SatisfactionTrendChartProps> = ({
  data = [],
  width = '148px',
  height = '150px',
  showGrid = false
}) => {
  const { t } = useTranslation();
  const chartData = data.length > 0 ? data : [];

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#318F1F',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      },
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>${t('home.satisfaction')}: ${data.value}%`;
      }
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      top: '10%',
      containLabel: true,
      show: showGrid,
      borderColor: '#DAE8D3'
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.date),
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: showGrid,
        fontSize: 12,
        color: '#666'
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: showGrid,
        fontSize: 12,
        color: '#666'
      },
      splitLine: {
        show: showGrid,
        lineStyle: {
          type: 'dashed',
          color: '#DAE8D3'
        }
      }
    },
    series: [
      {
        type: 'line',
        data: chartData.map(item => item.value),
        smooth: true,
        showSymbol: false,
        lineStyle: {
          color: '#318F1F',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(218, 232, 211, 0.8)' // 浅绿色，半透明
              },
              {
                offset: 1,
                color: 'rgba(218, 232, 211, 0.2)' // 更浅的绿色
              }
            ]
          }
        },
        emphasis: {
          focus: 'series',
          lineStyle: {
            width: 3
          }
        }
      }
    ]
  };

  return <ReactECharts option={option} style={{ width, height }} opts={{ renderer: 'canvas' }} />;
};
