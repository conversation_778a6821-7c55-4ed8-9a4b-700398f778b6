import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Tabs } from 'antd';
import ReactECharts from 'echarts-for-react';
import { getTrendColor, getTriangleStyle } from '@/utils/utils';
import { useMerchant } from '@/contexts/MerchantContext';
import { getWarningStats } from '@/api/dashboard';
import { MerchantReviewsStatistics } from '@/components/BusinessComponents/MerchantReviewsStatistics';
import { LiquidFillChart } from '@/components/UIComponents/LiquidFillChart';
import { CustomerType, PlatformEnums } from '@/types';
import { SatisfactionTrendChart } from '@/pages/merchant/home/<USER>/SatisfactionTrendChart';
import { Compass } from '@/components/UIComponents/Compass/Compass';
import { getPlatformSatisfaction, getPlatformSatisfactionTrend } from '@/api/satisfaction';
import {
  PlatformSatisfactionResponse,
  PlatformSatisfactionData,
  PlatformSatisfactionTrendResponse,
  CustomerTrendData,
  TrendDirection,
  WarningStatsResponse
} from '@/types/api';
import { useAuth } from '@/contexts/AuthContext';
import warningIcon from '@/assets/svg/warning.svg';
import WarningEmpty from '@/assets/svg/warning-empty.svg?react';
import { useTranslation } from 'react-i18next';

const platformsOrder = {
  [PlatformEnums.Google]: 0,
  [PlatformEnums.Facebook]: 1,
  [PlatformEnums.Yelp]: 2,
  [PlatformEnums.Xiaohongshu]: 3,
  [PlatformEnums.Amazon]: 4,
  [PlatformEnums.Other]: 5
};

const CardTitle = ({ title, size = 20, align = 'start' }: { title: string; size?: number; align?: 'start' | 'center' }) => {
  return (
    <div className={`text-${align} text-[${size}px] font-bold  mt-2`} style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
      {title}
    </div>
  );
};

const CustomerTypeTabs = ({
  customerType,
  handleCustomerTypeChange,
  className
}: {
  customerType: string;
  handleCustomerTypeChange: (activeKey: string) => void;
  className?: string;
}) => {
  const { t } = useTranslation();

  return (
    <Tabs
      type="card"
      activeKey={customerType}
      onChange={handleCustomerTypeChange}
      className={className}
      items={[
        { key: CustomerType.AllCustomers, label: t('home.allCustomers') },
        { key: CustomerType.ReturningCustomers, label: t('home.returningCustomers') }
      ]}
    />
  );
};

// 预警维度颜色映射
const warningColors = ['#768F1B', '#C9A94F', '#0D728F', '#86D4DB', '#CA4E33', '#38ADC2', '#CF9C70'];

export const MerchantHome = () => {
  const { merchant } = useMerchant();
  const { userContext } = useAuth();
  const { t } = useTranslation();

  // 分开两个独立的客户类型状态
  const [trendCustomerType, setTrendCustomerType] = useState<'allCustomers' | 'returningCustomers'>('allCustomers');
  const [satisfactionCustomerType, setSatisfactionCustomerType] = useState<'allCustomers' | 'returningCustomers'>('allCustomers');
  const [platformSatisfactionData, setPlatformSatisfactionData] = useState<PlatformSatisfactionResponse | null>(null);
  const [satisfactionTrendData, setSatisfactionTrendData] = useState<PlatformSatisfactionTrendResponse | null>(null);
  const [warningData, setWarningData] = useState<WarningStatsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [trendLoading, setTrendLoading] = useState(false);
  const [warningLoading, setWarningLoading] = useState(false);

  // 处理趋势客户类型切换
  const handleTrendCustomerTypeChange = (activeKey: string) => {
    setTrendCustomerType(activeKey as 'allCustomers' | 'returningCustomers');
  };

  // 处理满意度客户类型切换
  const handleSatisfactionCustomerTypeChange = (activeKey: string) => {
    setSatisfactionCustomerType(activeKey as 'allCustomers' | 'returningCustomers');
  };

  // 获取平台满意度数据
  const fetchPlatformSatisfaction = async () => {
    if (!merchant?.id) return;

    setLoading(true);
    try {
      const response = await getPlatformSatisfaction({
        merchantId: merchant.id.toString()
      });
      setPlatformSatisfactionData(response);
    } catch (error) {
      console.error('获取平台满意度数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取满意度趋势数据
  const fetchSatisfactionTrend = async () => {
    if (!merchant?.id) return;

    setTrendLoading(true);
    try {
      const response = await getPlatformSatisfactionTrend({
        merchantId: merchant.id.toString(),
        agentId: userContext?.agentId
      });
      setSatisfactionTrendData(response);
    } catch (error) {
      console.error('获取满意度趋势数据失败:', error);
    } finally {
      setTrendLoading(false);
    }
  };

  // 获取预警统计数据
  const fetchWarningStats = async () => {
    if (!merchant?.id) return;

    setWarningLoading(true);
    try {
      const response = await getWarningStats(merchant.id.toString());
      setWarningData(response);
    } catch (error) {
      console.error('获取预警统计数据失败:', error);
    } finally {
      setWarningLoading(false);
    }
  };

  useEffect(() => {
    fetchPlatformSatisfaction();
    fetchSatisfactionTrend();
    fetchWarningStats();
  }, [merchant?.id]);

  // 获取当前客户类型的平台数据（使用满意度tab的状态）
  const getCurrentPlatformData = (): PlatformSatisfactionData[] => {
    if (!platformSatisfactionData) return [];

    const currentData = (
      satisfactionCustomerType === 'allCustomers' ? platformSatisfactionData.allCustomers : platformSatisfactionData.returningCustomers
    )?.platforms;

    return (
      currentData?.sort(
        (a, b) =>
          platformsOrder[a.platformName as keyof typeof platformsOrder] - platformsOrder[b.platformName as keyof typeof platformsOrder]
      ) || []
    );
  };

  // 获取当前趋势客户类型的数据
  const getCurrentTrendData = (): CustomerTrendData | null => {
    if (!satisfactionTrendData) return null;

    return trendCustomerType === 'allCustomers' ? satisfactionTrendData.allCustomers : satisfactionTrendData.returningCustomers;
  };

  // 获取当前周的满意度数据
  const getCurrentWeekSatisfaction = () => {
    const trendData = getCurrentTrendData();
    if (!trendData) return { percentageInThreeMonth: 0, percentage: 0, change: 0, trendData: [] };

    return {
      percentageInThreeMonth: trendData.threeMonthOverview.averageSatisfactionPercentage,
      percentage: trendData.currentWeek.satisfactionPercentage,
      change: trendData.currentWeek.changeFromLastWeek,
      trendData: trendData.weeklyTrends.map(week => ({
        date: week.weekLabel,
        value: week.satisfactionPercentage
      })),
      trendDirection: trendData.currentWeek.trendDirection
    };
  };

  // 处理预警统计数据，转换为图表需要的格式，过滤掉0%的数据
  const getWarningChartData = () => {
    if (!warningData?.stats || warningData.stats.length === 0) {
      return [];
    }

    // 过滤掉百分比为0的数据，因为在饼图中不会显示且无法hover
    return warningData.stats
      .filter(stat => stat.percent > 0)
      .map(stat => {
        return {
          value: stat.percent,
          name: stat.dimension
        };
      });
  };

  // 预警饼图配置
  const warningPieChart = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}% ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      align: 'left',
      bottom: 30,
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 8,
      textStyle: {
        fontSize: 14,
        color: 'rgba(0, 0, 0, 0.88)'
      },
      formatter: function (name: string) {
        return name;
      }
    },
    series: [
      {
        name: t('home.warningCategories'),
        type: 'pie',
        radius: ['0%', '80%'],
        center: ['35%', '50%'],
        data: getWarningChartData(),
        label: {
          show: true,
          position: 'inside',
          formatter: '{c}%',
          fontSize: 14,
          fontWeight: 'bold',
          color: '#fff'
        },
        labelLine: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        color: warningColors
      }
    ],
    graph: {
      color: warningColors
    }
  };

  return (
    <div className=" flex flex-col gap-4">
      {/* 今日统计 */}
      <MerchantReviewsStatistics
        title={t('home.todayStats')}
        subTitle={t('home.todayTotalReviews')}
        timeChange={false}
        merchantId={merchant?.id}
        compareText={t('home.comparedToYesterday')}
      />
      {/* 满意度液体填充图表展示 */}

      {/* 客户满意度 */}
      <div className="flex flex-row justify-between items-center gap-4 h-[462px]">
        <Card className="w-[350px] flex-shrink-0 h-full">
          <CardTitle title={t('home.customerSatisfactionTrend')} />
          <CustomerTypeTabs customerType={trendCustomerType} handleCustomerTypeChange={handleTrendCustomerTypeChange} className="mt-3" />
          <div className="flex justify-center items-center">
            {trendLoading ? (
              <div className="w-[152px] h-[152px] flex items-center justify-center">
                <span>{t('common.loading')}</span>
              </div>
            ) : (
              <LiquidFillChart value={getCurrentWeekSatisfaction().percentageInThreeMonth} />
            )}
          </div>
          <CardTitle title={t('home.customerSatisfaction')} size={14} align="center" />
          <div className="mt-4">
            <div className="flex flex-row items-center justify-between">
              <div>
                <div>
                  <span className="text-sm text-gray-600">{t('home.thisWeekSatisfaction')}</span>
                  <span className="text-2xl font-bold">{getCurrentWeekSatisfaction().percentage.toFixed(1)}%</span>
                </div>
                <div className="flex items-center text-green-500 text-sm">
                  <span style={{ color: getTrendColor(getCurrentWeekSatisfaction().trendDirection === TrendDirection.DOWN ? -1 : 1) }}>
                    {getCurrentWeekSatisfaction().change.toFixed(1)}%
                  </span>
                  <span style={getTriangleStyle(getCurrentWeekSatisfaction().change)} className="ml-1" />
                  <span className="text-gray-500 ml-1">{t('home.comparedToLastWeek')}</span>
                </div>
              </div>
              <SatisfactionTrendChart data={getCurrentWeekSatisfaction().trendData} height="50px" />
            </div>
          </div>
        </Card>
        <Card className="flex-1  h-full">
          <CardTitle title={t('home.customerSatisfaction')} />
          <CustomerTypeTabs
            customerType={satisfactionCustomerType}
            handleCustomerTypeChange={handleSatisfactionCustomerTypeChange}
            className="mt-3"
          />
          <div className="grid grid-cols-3 gap-4">
            {loading ? (
              <div className="col-span-3 text-center py-8">{t('common.loading')}</div>
            ) : (
              getCurrentPlatformData().map((platform, index) => (
                <Compass
                  key={index}
                  value={platform.satisfactionPercentage ?? 0}
                  title={platform.platformNameCn}
                  platformName={platform.platformName}
                  totalReviews={platform.totalReviews}
                  satisfiedReviews={platform.satisfiedReviews}
                />
              ))
            )}
          </div>
        </Card>
      </div>
      {/* 本月预警消息 */}
      <Card className="h-fit">
        <CardTitle title={t('home.monthlyWarningMessages')} />
        {warningData?.warningSamples.length === 0 && warningData?.stats.length === 0 ? (
          <div className="flex flex-row items-center justify-center gap-5">
            <WarningEmpty className="w-[160px] h-[160px] mr-2" />
            <span>{t('home.noWarnings')}</span>
          </div>
        ) : (
          <div className="flex gap-6">
            <div className="w-[388px] flex-shrink-0">
              {warningLoading ? (
                <div className="h-[280px] flex items-center justify-center">
                  <span>{t('common.loading')}</span>
                </div>
              ) : (
                <ReactECharts option={warningPieChart} style={{ height: '280px' }} />
              )}
            </div>
            <div className="flex-1 ">
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center">
                  <img src={warningIcon} alt="warning" className="w-6 h-6 mr-2" />
                  <span className="text-sm font-medium">{t('home.warningMessages')}</span>
                </div>
                <Button className="text-blue-600 text-sm cursor-pointer" type="text" size="small" disabled>
                  {t('home.viewAll')}
                </Button>
              </div>
              <div className="space-y-2">
                {warningLoading ? (
                  <div className="text-center py-8">{t('common.loading')}</div>
                ) : warningData?.warningSamples && warningData.warningSamples.length > 0 ? (
                  warningData.warningSamples.slice(0, 6).map((sample, index) => (
                    <div key={sample.analysisId} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded text-sm">
                      <div className="flex items-center justify-start">
                        <span className="w-6 h-6 rounded-full text-xs flex items-center justify-center mr-3 bg-[#E4F5F5] text-[#0D728F] ">
                          {index + 1}
                        </span>

                        <span className="text-gray-900 mr-2 align-left overflow-hidden text-ellipsis whitespace-nowrap">
                          {sample?.warningType}
                        </span>
                      </div>

                      <div className="flex w-[260px] items-center justify-end text-gray-500 flex-shrink-0 gap-6">
                        <span className="text-gray-500 text-xs">{sample?.platform}</span>
                        <span className="text-xs">{sample?.createTime ? sample.createTime.replace('T', ' ').slice(0, 19) : ''}</span>
                        <Button className="ml-2" type="text" size="small" disabled>
                          →
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">{t('home.noWarningData')}</div>
                )}
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};
