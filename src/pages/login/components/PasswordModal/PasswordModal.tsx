import { useState, useCallback, useEffect, useMemo } from 'react';
import { Button, Input, Modal, Form, message } from 'antd';
import { sendResetPasswordCode, verifyResetPasswordCode, resetPassword } from '@/api/user';
import './index.css';
import DeleteSvg from '@/assets/svg/delete.svg?react';
import { useTranslation } from 'react-i18next';

interface IProps {
  visible: boolean;
  onCancel: () => void;
}

enum Step {
  EMAIL_VERIFY = 1,
  RESET_PASSWORD = 2
}

export const PasswordModal = ({ visible, onCancel }: IProps) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState<Step>(Step.EMAIL_VERIFY);
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [state, setState] = useState(''); // 用于存储验证接口返回的state
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  const [codeLoading, setCodeLoading] = useState(false);
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState({ email: '', verificationCode: '' });
  const emailValue = Form.useWatch('email', form);
  const newPassword = Form.useWatch('newPassword', form);
  const confirmPassword = Form.useWatch('confirmPassword', form);

  // 为每个密码输入框添加独立的 hover 状态
  const [newPasswordHovered, setNewPasswordHovered] = useState(false);
  const [confirmPasswordHovered, setConfirmPasswordHovered] = useState(false);

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // 重置状态
  const resetModal = useCallback(() => {
    setCurrentStep(Step.EMAIL_VERIFY);
    setEmail('');
    setVerificationCode('');
    setState('');
    setCountdown(0);
    form.resetFields();
    setFormValues({ email: '', verificationCode: '' });
  }, [form]);

  // 关闭弹窗
  const handleCancel = useCallback(() => {
    resetModal();
    onCancel();
  }, [resetModal, onCancel]);

  // 发送验证码
  const handleSendCode = useCallback(async () => {
    const emailValue = form.getFieldValue('email');
    if (!emailValue) {
      message.error(t('login.pleaseEnterEmailFirst'));
      return;
    }

    setCodeLoading(true);
    try {
      await sendResetPasswordCode(emailValue);
      setEmail(emailValue);
      setCountdown(60);
      message.success(t('login.verificationCodeSent'));
    } finally {
      setCodeLoading(false);
    }
  }, [form, t]);

  // 验证邮箱和验证码
  const handleVerifyEmailAndCode = useCallback(async () => {
    const values = await form.validateFields();
    setLoading(true);
    try {
      const stateResult = await verifyResetPasswordCode(values.email, values.verificationCode);
      setEmail(values.email);
      setVerificationCode(values.verificationCode);
      setState(stateResult); // 保存返回的state
      setCurrentStep(Step.RESET_PASSWORD);
      setFormValues(values);
      message.success(t('login.verificationSuccessful'));
    } finally {
      setLoading(false);
    }
  }, [form, t]);

  // 重置密码
  const handleResetPassword = useCallback(async () => {
    try {
      await form.validateFields();
      setLoading(true);
      await resetPassword(email, verificationCode, state, newPassword);
      message.success(t('login.passwordResetSuccessful'));
      handleCancel();
    } finally {
      setLoading(false);
    }
  }, [handleCancel, email, verificationCode, state, t, form, newPassword]);

  // 返回上一步
  const handleGoBack = useCallback(() => {
    if (currentStep === Step.RESET_PASSWORD) {
      setCurrentStep(Step.EMAIL_VERIFY);
      form.resetFields();
      form.setFieldValue('email', formValues.email);
      form.setFieldValue('verificationCode', formValues.verificationCode);
    } else {
      handleCancel();
    }
  }, [currentStep, form, handleCancel, formValues, t]);

  const title = useMemo(() => {
    return (
      <div className="text-base font-medium mb-6 w-full text-left">
        <span>{currentStep === Step.EMAIL_VERIFY ? t('login.forgotPasswordTitle') : t('login.changePassword')}</span>
      </div>
    );
  }, [currentStep, t]);

  // 渲染步骤1：输入邮箱和验证码
  const renderEmailVerifyStep = () => (
    <Form form={form} layout="vertical" className="w-full">
      <div className="text-base item-label">{t('login.enterEmail')}</div>
      <div className="flex gap-4 mb-4">
        <Form.Item
          name="email"
          rules={[
            { required: true, message: t('login.enterEmail') },
            { type: 'email', message: t('login.enterValidEmail') }
          ]}
          validateTrigger="onBlur"
        >
          <Input placeholder={t('login.enterEmail')} size="large" className="h-[32px]" />
        </Form.Item>
        <Button
          onClick={handleSendCode}
          disabled={countdown > 0}
          loading={codeLoading}
          size="large"
          className="w-fit h-[32px] "
          type="primary"
        >
          {countdown > 0 ? `${countdown}s` : t('login.sendVerificationCode')}
        </Button>
      </div>

      <div className="text-base mb-2">{t('login.verificationCode')}</div>
      <Form.Item name="verificationCode" rules={[{ required: true, message: t('login.enterVerificationCode') }]} validateTrigger="onBlur">
        <Input placeholder={t('login.enterVerificationCode')} size="large" />
      </Form.Item>
    </Form>
  );

  // 渲染步骤2：重置密码
  const renderResetPasswordStep = () => (
    <Form form={form} layout="vertical">
      <div className="text-base mb-2 item-label">{t('login.newPassword')}</div>
      <Form.Item
        name="newPassword"
        rules={[
          { required: true, message: t('login.enterNewPassword') },
          {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
            message: t('login.passwordRequirements')
          }
        ]}
        validateTrigger="onBlur"
      >
        <div className="relative" onMouseEnter={() => setNewPasswordHovered(true)} onMouseLeave={() => setNewPasswordHovered(false)}>
          <Input.Password
            value={newPassword}
            placeholder={t('login.enterNewPassword')}
            size="large"
            onChange={e => {
              form.setFieldValue('newPassword', e.target.value);
            }}
          />
          {newPassword && newPasswordHovered && (
            <DeleteSvg
              className="absolute right-9 top-[14px] cursor-pointer z-10 w-[13px] h-[13px]"
              onClick={() => {
                form.setFieldValue('newPassword', '');
              }}
            />
          )}
        </div>
      </Form.Item>

      <div className="text-base mb-2 item-label">{t('login.confirmPassword')}</div>
      <Form.Item
        name="confirmPassword"
        dependencies={['newPassword']}
        rules={[
          { required: true, message: t('login.enterPasswordAgain') },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('newPassword') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(t('login.passwordsDoNotMatch')));
            }
          })
        ]}
        validateTrigger="onBlur"
      >
        <div
          className="relative"
          onMouseEnter={() => setConfirmPasswordHovered(true)}
          onMouseLeave={() => setConfirmPasswordHovered(false)}
        >
          <Input.Password
            value={confirmPassword}
            placeholder={t('login.enterPasswordAgain')}
            size="large"
            onChange={e => {
              form.setFieldValue('confirmPassword', e.target.value);
            }}
          />
          {confirmPassword && confirmPasswordHovered && (
            <DeleteSvg
              className="absolute right-9 top-[14px] cursor-pointer z-10 w-[13px] h-[13px]"
              onClick={() => {
                form.setFieldValue('confirmPassword', '');
              }}
            />
          )}
        </div>
      </Form.Item>
    </Form>
  );

  const renderContent = () => {
    switch (currentStep) {
      case Step.EMAIL_VERIFY:
        return renderEmailVerifyStep();
      case Step.RESET_PASSWORD:
        return renderResetPasswordStep();
      default:
        return renderEmailVerifyStep();
    }
  };

  return (
    <Modal open={visible} onCancel={handleCancel} footer={null} width={592} height={420} centered>
      <div className="p-6  flex flex-col items-center justify-between ">
        {title}
        <div className="w-[292px] password-modal-content m-auto">{renderContent()}</div>
      </div>
      <div className="flex justify-end gap-3">
        <Button onClick={currentStep === Step.EMAIL_VERIFY ? handleCancel : handleGoBack} size="large">
          {t('login.goBack')}
        </Button>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          size="large"
          disabled={currentStep === Step.EMAIL_VERIFY && !emailValue}
          onClick={currentStep === Step.EMAIL_VERIFY ? handleVerifyEmailAndCode : handleResetPassword}
        >
          确认
        </Button>
      </div>
    </Modal>
  );
};
