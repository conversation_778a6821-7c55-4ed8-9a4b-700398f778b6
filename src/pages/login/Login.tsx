import { useCallback, useEffect, useMemo, useState } from 'react';
import { Form, Input, Button, message, ConfigProvider, Checkbox } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useLocation, Link, useNavigate } from 'react-router-dom';
import loginBackground from '../../assets/login-background.png';
import logo from '../../assets/logo.png';
import { ActiveText } from '@/components/ActiveText';
import { PasswordModal } from './components/PasswordModal/PasswordModal';
import { LanguageSwitcher } from '@/components/BusinessComponents/LanguageSwitch';
import { useTranslation } from 'react-i18next';
import { LanguageCode } from '@/types';
import i18n from '@/i18n';

const Login = () => {
  const [loading, setLoading] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const { login, currentUser, loading: authLoading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const from = location.state?.from?.pathname || '/index';
  const { t } = useTranslation();

  const initialLanguage = useMemo(() => {
    const urlParams = new URLSearchParams(location.search);
    const langParam = urlParams.get('lang');
    return langParam as LanguageCode;
  }, [location.search]);

  // 语言切换
  useEffect(() => {
    if (initialLanguage) {
      const validLanguages = Object.values(LanguageCode);
      if (validLanguages.includes(initialLanguage as LanguageCode)) {
        i18n.changeLanguage(initialLanguage);
      }
    }
  }, [initialLanguage, i18n]);

  // 如果用户已经登录，直接跳转
  useEffect(() => {
    if (currentUser && !authLoading) {
      navigate(from, { replace: true });
    }
  }, [currentUser, authLoading, navigate, from]);

  const onFinish = useCallback(
    async (values: { account: string; password: string; remember: boolean }) => {
      if (loading) return;
      setLoading(true);
      try {
        const isSuccess = await login(values.account, values.password, values.remember);
        if (isSuccess) {
          message.success(t('login.loginSuccessful'));
          navigate(from, { replace: true });
        }
      } finally {
        setLoading(false);
      }
    },
    [login, t, loading]
  );

  const handleForgotPassword = useCallback(() => {
    setPasswordModalVisible(true);
  }, []);

  const handlePasswordModalClose = useCallback(() => {
    setPasswordModalVisible(false);
  }, []);

  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            colorPrimary: '#ff5722',
            colorPrimaryHover: '#f44336',
            colorPrimaryActive: '#e53935'
          },
          Checkbox: {
            colorPrimary: '#f44321',
            colorPrimaryHover: '#f44321'
          },
          Input: {
            activeBorderColor: '#f44321',
            hoverBorderColor: '#f44321'
          }
        }
      }}
    >
      <div
        className="min-h-screen flex flex-col justify-between relative login-page"
        style={{
          backgroundImage: `url(${loginBackground})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        {/* 灰色蒙版 */}
        <div className="absolute inset-0  z-0 bg-black/50"></div>
        {/* 语言切换器 - 右上角 */}
        <div className="absolute top-12 right-12 z-50">
          <LanguageSwitcher initialLanguage={initialLanguage} />
        </div>
        <div className="flex-1 flex flex-col items-center flex-start mt-10 z-10">
          <div className="w-[430px] mx-auto">
            <div className="flex items-center justify-center mb-6">
              <div className="flex items-center">
                <img src={logo} alt="DAN Social" className="h-[100px]" />
                <h2 className="text-white text-xl ml-4 z-10">{t('login.loginToYourAccount')}</h2>
              </div>
            </div>
            <div className="text-center text-white text-sm mb-8">{t('login.orSignInWithEmail')}</div>
            <Form name="login" initialValues={{ remember: true }} onFinish={onFinish} layout="vertical" style={{ width: '100%' }}>
              <Form.Item
                name="account"
                label={<span className="text-white">{t('login.email')}</span>}
                rules={[{ required: true, message: t('login.pleaseInputEmail') }]}
                required={false}
              >
                <Input
                  prefix={<UserOutlined className="text-gray-400" />}
                  placeholder="<EMAIL>"
                  size="large"
                  className="rounded border-white/30"
                  style={{
                    color: '#E0E0E0',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }}
                  spellCheck={false}
                />
              </Form.Item>
              <Form.Item
                name="password"
                label={<span className="text-white">{t('login.password')}</span>}
                rules={[{ required: true, message: t('login.pleaseInputPassword') }]}
                required={false}
              >
                <Input.Password
                  prefix={<LockOutlined className="text-gray-400" />}
                  placeholder="••••••••••••"
                  size="large"
                  className="rounded border-white/30"
                  style={{
                    color: '#E0E0E0',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }}
                  spellCheck={false}
                />
              </Form.Item>
              <div className="flex justify-between items-center mb-6 -mt-5">
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox className="text-white">{t('login.rememberMe')}</Checkbox>
                </Form.Item>
                <div className="text-orange-500 hover:text-orange-400 cursor-pointer" onClick={handleForgotPassword}>
                  <ActiveText text={t('login.forgotPassword')} />
                </div>
              </div>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} size="large" block className="h-12 rounded">
                  {t('login.login')}
                </Button>
              </Form.Item>
            </Form>
            <div className="text-center mt-4">
              <Link to="mailto:<EMAIL>" className="text-orange-500 hover:text-orange-400">
                <ActiveText text={t('login.contactUs')} />
              </Link>
            </div>
          </div>
        </div>
        <div className="text-center text-white mb-[100px]">©copyright</div>
        <PasswordModal visible={passwordModalVisible} onCancel={handlePasswordModalClose} />
      </div>
    </ConfigProvider>
  );
};

export default Login;
