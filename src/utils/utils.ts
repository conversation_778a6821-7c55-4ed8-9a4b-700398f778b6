import CryptoJS from 'crypto-js';
import { SECRET_KEY } from './constants';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import updateLocale from 'dayjs/plugin/updateLocale';
import { StarRating } from '@/types/review';


dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(updateLocale);
// 设置周一为一周的开始
dayjs.updateLocale('en', {
  weekStart: 1  // 1 表示周一，0 表示周日
});

export const encryptData = (data: any): string => {
  return CryptoJS.AES.encrypt(JSON.stringify(data), SECRET_KEY).toString();
};

export const decryptData = (encryptedData: string): any => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
  return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
};

export const formatDateTime = (isoString: string) => {
  return new Date(isoString).toLocaleString();
};

export const formatDate = (isoString: string) => {
  return dayjs(isoString).format('YYYY/MM/DD');
};

export const formatDateText = (dateString: string) => {
  return dayjs(dateString).format('YYYY年M月D日');
};

export const formatDateTimeString = (timeStr: string) => {
  return dayjs(timeStr).format('YYYY/MM/DD HH:mm');
};


const trendColor = {
  increase: '#318F1F',
  decrease: '#A43420',
  same: '#666'
};

export const getTrendColor = (changed: number) => {
  if (changed > 0) {
    return trendColor.increase;
  } else if (changed < 0) {
    return trendColor.decrease;
  } else {
    return trendColor.same;
  }
};

export const getTriangleStyle = (changed: number) => {
  const baseStyle = {
    width: 0,
    height: 0,
    display: 'inline-block',
    verticalAlign: 'middle'
  };

  if (changed >= 0) {
    return {
      ...baseStyle,
      borderLeft: '2.5px solid transparent',
      borderRight: '2.5px solid transparent',
      borderBottom: `2.5px solid ${trendColor.increase}`
    };
  } else {
    return {
      ...baseStyle,
      borderLeft: '2.5px solid transparent',
      borderRight: '2.5px solid transparent',
      borderTop: `2.5px solid ${trendColor.decrease}`
    };
  }
};

// 根据满意度百分比返回对应颜色
export const getSatisfactionColor = (percentage: number): string => {
  if (percentage > 60) return '#1597B4';
  if (percentage >= 20) return '#318F1F';
  return '#B6354C';
};

// 获取环境前缀
const getEnvironmentPrefix = (): string => {
  const hostname = window.location.hostname;
  if (hostname.includes('test.dan-social.glor.cn')) {
    return 'test_';
  }
  return 'dev_';
};

// 存储工具函数 - 支持localStorage和sessionStorage
export const storageUtils = {
  setItem: (key: string, value: string, persistent: boolean = true): void => {
    const prefixedKey = `${getEnvironmentPrefix()}${key}`;
    if (persistent) {
      localStorage.setItem(prefixedKey, value);
    } else {
      sessionStorage.setItem(prefixedKey, value);
    }
  },

  getItem: (key: string): string | null => {
    const prefixedKey = `${getEnvironmentPrefix()}${key}`;
    // 先尝试从localStorage获取，再尝试从sessionStorage获取
    return localStorage.getItem(prefixedKey) || sessionStorage.getItem(prefixedKey);
  },

  removeItem: (key: string): void => {
    const prefixedKey = `${getEnvironmentPrefix()}${key}`;
    localStorage.removeItem(prefixedKey);
    sessionStorage.removeItem(prefixedKey);
  },

  clear: (): void => {
    const prefix = getEnvironmentPrefix();
    const keysToRemove: string[] = [];

    // 收集localStorage中所有以当前环境前缀开头的key
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key);
      }
    }

    // 收集sessionStorage中所有以当前环境前缀开头的key
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key);
      }
    }

    // 删除这些key
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });
  }
};

// 辅助函数：将星级评分转换为数字
export const getStarRatingValue = (starRating: StarRating): number => {
  switch (starRating) {
    case StarRating.ONE:
      return 1;
    case StarRating.TWO:
      return 2;
    case StarRating.THREE:
      return 3;
    case StarRating.FOUR:
      return 4;
    case StarRating.FIVE:
      return 5;
    default:
      return 0;
  }
};