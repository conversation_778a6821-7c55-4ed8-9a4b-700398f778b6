import { storageUtils } from './utils';
import { RememberMeData, REMEMBER_ME_KEY, REMEMBER_ME_EXPIRY_DAYS } from '@/types/api';

/**
 * Remember Me 功能工具类
 * 统一管理记住登录功能的所有逻辑
 */
export class RememberMeManager {
  /**
   * 检查记住登录数据是否过期
   * @param timestamp 保存时的时间戳
   * @returns 是否过期
   */
  private static isExpired(timestamp: number): boolean {
    const now = Date.now();
    const expiryTime = timestamp + REMEMBER_ME_EXPIRY_DAYS * 24 * 60 * 60 * 1000;
    return now > expiryTime;
  }

  /**
   * 保存记住登录信息
   * @param token 用户token
   * @param username 用户名
   * @param password 密码
   */
  static save(token: string, username: string, password: string): void {
    try {
      const rememberMeData: RememberMeData = {
        token,
        username,
        password,
        timestamp: Date.now()
      };
      storageUtils.setItem(REMEMBER_ME_KEY, JSON.stringify(rememberMeData));
    } catch (error) {
      console.error('Failed to save remember me data:', error);
    }
  }

  /**
   * 获取记住登录信息
   * @returns 记住登录数据，如果不存在或已过期则返回null
   */
  static get(): RememberMeData | null {
    try {
      const data = storageUtils.getItem(REMEMBER_ME_KEY);
      if (!data) return null;

      const rememberMeData: RememberMeData = JSON.parse(data);

      // 检查是否过期
      if (this.isExpired(rememberMeData.timestamp)) {
        this.clear();
        return null;
      }

      return rememberMeData;
    } catch (error) {
      console.error('Failed to parse remember me data:', error);
      this.clear();
      return null;
    }
  }

  /**
   * 检查是否存在有效的记住登录数据
   * @returns 是否存在有效数据
   */
  static hasValidData(): boolean {
    return this.get() !== null;
  }

  /**
   * 更新记住登录数据中的token（保持其他信息不变）
   * @param newToken 新的token
   * @returns 是否更新成功
   */
  static updateToken(newToken: string): boolean {
    try {
      const existingData = this.get();
      if (!existingData) return false;

      this.save(newToken, existingData.username, existingData.password);
      return true;
    } catch (error) {
      console.error('Failed to update remember me token:', error);
      return false;
    }
  }

  /**
   * 清除记住登录信息
   */
  static clear(): void {
    try {
      storageUtils.removeItem(REMEMBER_ME_KEY);
    } catch (error) {
      console.error('Failed to clear remember me data:', error);
    }
  }

  /**
   * 清除所有认证相关的本地存储数据
   * 包括用户信息、token、记住登录数据等
   */
  static clearAllAuthData(): void {
    try {
      storageUtils.removeItem('user');
      storageUtils.removeItem('token');
      storageUtils.removeItem('merchantId');
      storageUtils.removeItem('agentId');
      this.clear();
    } catch (error) {
      console.error('Failed to clear all auth data:', error);
    }
  }

  /**
   * 获取记住登录数据的剩余有效时间（毫秒）
   * @returns 剩余时间，如果数据不存在或已过期则返回0
   */
  static getRemainingTime(): number {
    const data = this.get();
    if (!data) return 0;

    const now = Date.now();
    const expiryTime = data.timestamp + REMEMBER_ME_EXPIRY_DAYS * 24 * 60 * 60 * 1000;
    const remaining = expiryTime - now;
    
    return Math.max(0, remaining);
  }

  /**
   * 获取记住登录数据的剩余有效天数
   * @returns 剩余天数，如果数据不存在或已过期则返回0
   */
  static getRemainingDays(): number {
    const remainingMs = this.getRemainingTime();
    return Math.ceil(remainingMs / (24 * 60 * 60 * 1000));
  }
}
