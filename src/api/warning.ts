import api from './apiClient';
import type {
  ApiResponse,
  GetWarningListParams,
  WarningMessageBaseParams,
  PageWarningMessageSimpleResponse,
  WarningMessageDetailDTO
} from '@/types';

// 获取预警消息简单列表
export const getWarningList = async (params?: GetWarningListParams): Promise<PageWarningMessageSimpleResponse | null> => {
  const response = await api.get<GetWarningListParams, ApiResponse<PageWarningMessageSimpleResponse>>('/warning/list', {
    params
  });
  return response?.data;
};

// 获取预警消息详情
export const getWarningDetail = async (params: WarningMessageBaseParams): Promise<WarningMessageDetailDTO> => {
  const response = await api.get<WarningMessageBaseParams, ApiResponse<WarningMessageDetailDTO>>('/warning/detail', {
    params
  });
  return response?.data;
};

// 标记预警为已读
export const markWarningAsRead = async (data: WarningMessageBaseParams): Promise<void> => {
  await api.post('/warning/read', data);
};
