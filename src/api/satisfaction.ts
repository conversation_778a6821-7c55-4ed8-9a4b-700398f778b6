import { RadarChartDataResponse, PlatformSatisfactionResponse, PlatformSatisfactionTrendResponse } from "@/types/api";
import api from "./apiClient";



// 获取客户满意度雷达图数据
export const getSatisfactionRadarChart = async (data?: {
  platform?: string;
  merchantId?: number;
  agentId?: number;
  userId?: number;
}): Promise<RadarChartDataResponse> => {
  const res = await api.post('/customer-satisfaction/radar/multi-platform', data);
  return res.data;
};

export const getPlatformSatisfaction = async (params?: {
  merchantId?: string;
  agentId?: number;
  userId?: number;
  startTime?: string;
  endTime?: string;
}): Promise<PlatformSatisfactionResponse> => {
  const res = await api.post('/customer-satisfaction/percentage/data', params);
  return res.data;
};

export const getPlatformSatisfactionTrend = async (params?: {
  merchantId?: string;
  agentId?: number;
  userId?: number;
  startTime?: string;
  endTime?: string;
  customerType?: string;
}): Promise<PlatformSatisfactionTrendResponse> => {
  const res = await api.post('/customer-satisfaction/trend/data', params);
  return res.data;
};