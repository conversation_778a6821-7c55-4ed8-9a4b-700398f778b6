import api from './apiClient';
import type {
  ApiResponse,
  GetGoogleLocationsParams,
  PageMerchantGoogleLocationResponse,
  GoogleAccount,
  GoogleLocationData,
  GoogleMerchantLocation,
  MerchantSearchParams,
  Merchant,
  MerchantHistoryResponse,
  PlatformType,
  ToneResponse,
  MerchantSetToneRequest,
  MerchantSettingToneResponse,
  FeedbackRequest,
  FeedbackResponse,
  MerchantSatisfactionRankingRequest,
  MerchantSatisfactionRankingResponse
} from '@/types';


// 获取所有商户（不分页，用于下拉选择）
export const getAllMerchants = async (title?: string): Promise<Merchant[]> => {
  try {
    // 获取大量数据，用于下拉选择
    const response = await api.get<MerchantSearchParams, ApiResponse<Merchant[]>>('/merchants', {
      params: {
        title
      }
    });

    return response?.data || [];

  } catch (error) {
    console.error('Failed to fetch all merchants:', error);
    return [];
  }
};

// 获取商户搜索历史记录
export const getMerchantHistory = async (): Promise<Merchant[]> => {
  try {
    const response = await api.get<any, ApiResponse<MerchantHistoryResponse>>('/p/merchant/history');
    if (response?.data?.content) {
      return response.data.content?.map(item => ({
        id: item.merchantId,
        title: item.title
      })) || []
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch merchant history:', error);
    return [];
  }
};

// 保存商户搜索历史记录
export const saveMerchantHistory = async (merchantId: number): Promise<void> => {
  try {
    await api.post(`/p/merchant/history`, { merchantId });
  } catch (error) {
    console.error('Failed to save merchant history:', error);
    throw error;
  }
};

// 获取所有Google绑定账号
export const getAllGoogleAccounts = async (): Promise<GoogleAccount[]> => {
  try {
    const response = await api.get<void, ApiResponse<GoogleAccount[]>>('/g/accounts');

    return response?.data || [];
  } catch (error) {
    console.error('Failed to fetch Google accounts:', error);
    return [];
  }
};

// 获取分页Google店铺列表
export const getGoogleLocations = async (params?: GetGoogleLocationsParams): Promise<PageMerchantGoogleLocationResponse | null> => {
  try {
    const response = await api.get<GetGoogleLocationsParams, ApiResponse<PageMerchantGoogleLocationResponse>>('/g/accounts/locations', {
      params
    });

    return response?.data || null;
  } catch (error) {
    console.error('Failed to fetch Google locations:', error);
    return null;
  }
};

export const getGoogleAuthUrl = async (): Promise<string> => {
  try {
    const response = await api.get<void, ApiResponse<string>>('/g/accounts/build-auth-url');
    return response?.data || '';
  } catch (error) {
    console.error('Failed to fetch Google auth URL:', error);
    return '';
  }
};

export const submitGoogleAuth = async (code: string, state: string): Promise<{
  id?: number,
  email?: string,
} | undefined> => {
  try {
    const response = await api.post<void, ApiResponse<{
      id: number,
      email: string,
    }>>('/g/accounts/auth', { code, state });
    return response?.data || {};
  } catch (error) {
    console.error('Failed to fetch Google auth token:', error);
  }
};

// 解绑Google店铺
export const unbindGoogleLocation = async (locationId: string | number): Promise<boolean> => {
  try {
    await api.delete(`/merchants/unbind/${locationId}/locations`);
    return true;
  } catch (error) {
    console.error('Failed to unbind Google location:', error);
    throw error;
  }
};

// 根据Google账号ID获取店铺列表（所有店铺，用于选择）
export const getAllGoogleLocationsByAccount = async (gAccountId: number): Promise<GoogleLocationData[]> => {
  try {
    const response = await api.get<{ gAccountId: number }, ApiResponse<GoogleLocationData[]>>('/g/locations', {
      params: {
        gAccountId
      }
    });

    return response?.data || [];
  } catch (error) {
    console.error('Failed to fetch Google locations by account:', error);
    return [];
  }
};

// 根据Google账号ID获取已绑定的店铺列表
export const getGoogleLocationsByAccountId = async (gAccountId: number): Promise<GoogleMerchantLocation[]> => {
  try {
    const response = await api.get<void, ApiResponse<GoogleMerchantLocation[]>>(`/g/accounts/locations/${gAccountId}`);

    return response?.data || [];
  } catch (error) {
    console.error('Failed to fetch Google locations by account ID:', error);
    return [];
  }
};

// 提交绑定Google店铺
export const bindGoogleLocations = async (data: {
  type: PlatformType;
  locations: GoogleLocationData[];
}): Promise<boolean> => {
  try {
    await api.post('/merchants/locations', data);
    return true;
  } catch (error) {
    console.error('Failed to bind Google locations:', error);
    throw error;
  }
};

// 获取系统语气列表
export const getSystemTones = async (): Promise<ToneResponse | null> => {
  try {
    const response = await api.get<void, ApiResponse<ToneResponse>>('/merchant-customize-setting/tone');
    return response?.data || null;
  } catch (error) {
    console.error('Failed to fetch system tones:', error);
    return null;
  }
};

// 获取商户语气设置
export const getMerchantToneSetting = async (merchantId: number): Promise<MerchantSettingToneResponse | null> => {
  try {
    const response = await api.get<void, ApiResponse<MerchantSettingToneResponse>>(`/merchant-customize-setting/tone/${merchantId}`);
    return response?.data || null;
  } catch (error) {
    console.error('Failed to fetch merchant tone setting:', error);
    return null;
  }
};

// 设置商户语气
export const setMerchantTone = async (data: MerchantSetToneRequest): Promise<boolean> => {
  try {
    await api.post('/merchant-customize-setting/tone', data);
    return true;
  } catch (error) {
    console.error('Failed to set merchant tone:', error);
    throw error;
  }
};

// 提交反馈
export const submitFeedback = async (data: FeedbackRequest): Promise<boolean> => {
  try {
    await api.post<FeedbackRequest, ApiResponse<FeedbackResponse>>('/feedback', data);
    return true;
  } catch (error) {
    console.error('Failed to submit feedback:', error);
    throw error;
  }
};

// 获取商户满意度排名
export const getMerchantSatisfactionRanking = async (
  params: MerchantSatisfactionRankingRequest
): Promise<MerchantSatisfactionRankingResponse | null> => {
  try {
    const response = await api.post<MerchantSatisfactionRankingRequest, ApiResponse<MerchantSatisfactionRankingResponse>>(
      '/merchant-satisfaction/ranking',
      params
    );
    return response?.data || null;
  } catch (error) {
    console.error('Failed to fetch merchant satisfaction ranking:', error);
    return null;
  }
};

