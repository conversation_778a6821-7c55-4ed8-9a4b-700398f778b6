import { decryptData, storageUtils } from '@/utils/utils';
import axios from 'axios';
import { message } from 'antd';

// 创建环境配置
const API_CONFIG = {
  development: {
    baseURL: '/api' // 使用相对路径，通过Vite代理转发
  },
  production: {
    baseURL: '/api' // 生产环境通常使用相对路径，避免跨域
  }
};

// 获取当前环境
const currentEnv = process.env.NODE_ENV || 'development';
const apiConfig = API_CONFIG[currentEnv as keyof typeof API_CONFIG];

// 不需要token的接口白名单
const NO_TOKEN_URLS = ['/user/login'];

// 记住登录的key
export const REMEMBER_ME_KEY = 'rememberMe';

// 创建axios实例
const api = axios.create({
  baseURL: apiConfig.baseURL,
  withCredentials: true, // 允许跨域请求携带凭证
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to add token to all requests
api.interceptors.request.use(
  config => {
    // 检查当前请求是否在白名单中
    const isNoTokenUrl = NO_TOKEN_URLS.some(url => config.url?.includes(url));

    if (!isNoTokenUrl) {
      let token = storageUtils.getItem('token');
      if (token) {
        try {
          token = decryptData(token);
        } catch (error) {
          console.error('Token decryption failed:', error);
        }
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle token expiration
api.interceptors.response.use(
  (response) => {
    return response;
  },
  error => {
    // 处理401未授权错误（token失效）
    if (error.response?.status === 401) {
      // 清除本地存储的用户信息和token
      storageUtils.removeItem('user');
      storageUtils.removeItem('token');
      storageUtils.removeItem('merchantId');
      storageUtils.removeItem('agentId');

      // 清除记住登录信息，因为token已过期
      storageUtils.removeItem(REMEMBER_ME_KEY);

      // 跳转到登录页面，保持当前的 URL 参数
      const currentSearch = window.location.search;
      const loginUrl = currentSearch ? `/login${currentSearch}` : '/login';
      window.location.replace(loginUrl);

      // 返回一个被拒绝的Promise，终止后续处理
      return Promise.reject(new Error('Token expired, redirecting to login'));
    }
    message.error(error?.response?.data?.message || '请求失败');
    return Promise.reject(error);
  }
);

export default api;
