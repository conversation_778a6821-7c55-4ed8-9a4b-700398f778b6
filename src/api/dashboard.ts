import api from './apiClient';
import {
  AllCommentsResponse,
  StatisticsResponse,
  CustomerSatisfactionStatisticsResponse,
  AllCommentsRequest,
  ApiResponse,
  WarningStatsResponse,
  CustomerSatisfactionStatisticsRequest
} from '@/types';

// 获取商户统计
export const getMerchantStatistics = async (): Promise<StatisticsResponse> => {
  const response = await api.get('/agent/dashboard/merchant-statistics');
  return response.data;
};

// 获取当日评价统计
export const getTodayComments = async (): Promise<StatisticsResponse> => {
  const response = await api.get('/agent/dashboard/today-comments');
  return response.data;
};

// 获取全部评论统计
export const getAllComments = async (
  params: AllCommentsRequest
): Promise<AllCommentsResponse> => {
  const response = await api.get('/agent/dashboard/all-comments', {
    params,
  });
  return response.data;
};

// 获取客户满意度统计
export const getCustomerSatisfaction = async (params?: CustomerSatisfactionStatisticsRequest): Promise<CustomerSatisfactionStatisticsResponse> => {
  const response = await api.post('/customer-satisfaction/statistics/data', params);
  return response.data;
};



// 获取评论预警统计信息
export const getWarningStats = async (merchantId?: string): Promise<WarningStatsResponse | null> => {
  try {
    const response = await api.get<{ merchantId?: string }, ApiResponse<WarningStatsResponse>>('/review-analysis/warning-stats', {
      params: {
        merchantId
      }
    });

    return response?.data || null;
  } catch (error) {
    console.error('Failed to fetch warning stats:', error);
    return null;
  }
};