import { MerchantLocation } from '@/types/merchant';
import api from './apiClient';
import type {
  ApiResponse,
  GoogleReviewReplyParams,
  GetGoogleReviewsResponse,
  GetGoogleReviewsParams,
  GetReviewStatisticsParams,
  ReviewStatisticsResponse
} from '@/types';

// 获取商户的店铺列表
export const getMerchantLocations = async (merchantId: number): Promise<MerchantLocation[]> => {
  try {
    const response = await api.get(`/merchants/${merchantId}/locations`);
    return response?.data || [];
  } catch (error) {
    console.error('Failed to fetch merchant locations:', error);
    return [];
  }
};

// 获取Google评论接口
export const getGoogleReviews = async (params: GetGoogleReviewsParams): Promise<GetGoogleReviewsResponse | null> => {
  try {
    const response = await api.get<GetGoogleReviewsParams, ApiResponse<GetGoogleReviewsResponse>>('/g/comments', {
      params
    });

    if (response?.data) {
      return response.data;
    }
    return null;
  } catch (error) {
    return null;
  }
};

// 回复Google评论接口
export const replyGoogleReview = async (data: GoogleReviewReplyParams): Promise<boolean> => {
  try {
    await api.post('/g/comments/reply', data);
    return true;
  } catch (error) {
    console.error('Reply failed:', error);
    return false;
  }
};

// 获取AI回复接口
export const getAIReply = async (id: string): Promise<string | null> => {
  try {
    const response = await api.get(`/g/comments/get-reply/${id}`);
    return response?.data || null;
  } catch (error) {
    console.error('Failed to get AI reply:', error);
    return null;
  }
};

// 获取评论统计接口
export const getReviewStatistics = async (params: GetReviewStatisticsParams): Promise<ReviewStatisticsResponse | null> => {
  try {
    const response = await api.get<GetReviewStatisticsParams, ApiResponse<ReviewStatisticsResponse>>('/agent/review-statistics', {
      params
    });

    if (response?.data) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error('Failed to get review statistics:', error);
    return null;
  }
};
