import api from './apiClient';
import type {
  User,
  loginParams,
  ApiResponse,
  MenuItem,
  UserContext,
  SwitchRoleParams,
  UserPackageInfoResponse,
  PageSystemMessageResponse,
  GetSystemMessageParams,
  SystemMessageReadRequest,
  ResetPasswordSendRequest,
  ResetPasswordVerifyRequest,
  ResetPasswordResetRequest,
  UserPreferSettingUpdateRequest,
  UserPreferenceUpdateType,
  UserInfoResponse
} from '@/types';

// 登录接口
export const login = async (account: string, password: string): Promise<User | null> => {
  const response = await api.post<loginParams, ApiResponse<User>>('/user/login', {
    account,
    password
  });

  if (response?.data) {
    return response.data;
  }
  return null;
};

// 登出接口
export const logout = async (): Promise<boolean> => {
  await api.post('/user/logout');
  return true;
};

// 获取用户上下文信息
export const getUserContext = async (): Promise<UserContext | null> => {
  const response = await api.get<void, ApiResponse<UserContext>>('/user/context');
  if (response?.data) {
    return response.data;
  }
  return null;
};

// 获取用户套餐信息
export const getUserPackageInfo = async (): Promise<UserPackageInfoResponse | null> => {
  try {
    const response = await api.get<void, ApiResponse<UserPackageInfoResponse>>('/user/package-info');
    return response?.data || null;
  } catch (error) {
    console.error('Failed to fetch user package info:', error);
    return null;
  }
};

// 切换角色
export const switchRole = async (roleId: number, merchantId: number): Promise<boolean> => {
  await api.post<SwitchRoleParams, ApiResponse<void>>('/role/switch', {
    roleId, merchantId
  });
  return true;
};

// 清除当前角色
export const clearRole = async (): Promise<boolean> => {
  await api.post<void, ApiResponse<void>>('/role/clear');
  return true;
};

// 获取Agent菜单树
export const getMenus = async (): Promise<MenuItem[]> => {
  const response = await api.get<void, ApiResponse<MenuItem[]>>('/menu/user/tree');
  if (response?.data) {
    return response.data;
  }
  return [];
};


// 获取消息中心badge
export const getSystemMessageBadge = async (): Promise<boolean> => {
  try {
    const response = await api.get<void, ApiResponse<boolean>>('/system-message/badge');
    return response?.data;
  } catch (error) {
    return false;
  }
};

// 获取系统消息列表
export const getSystemMessages = async (params: GetSystemMessageParams): Promise<PageSystemMessageResponse | null> => {
  try {
    const response = await api.get<GetSystemMessageParams, ApiResponse<PageSystemMessageResponse>>('/system-message', {
      params
    });
    return response?.data || null;
  } catch (error) {
    console.error('Failed to fetch system messages:', error);
    return null;
  }
};

// 更新消息已读状态
export const markSystemMessageAsRead = async (data: SystemMessageReadRequest): Promise<boolean> => {
  try {
    await api.post<SystemMessageReadRequest, ApiResponse<void>>('/system-message', data);
    return true;
  } catch (error) {
    console.error('Failed to mark message as read:', error);
    throw error;
  }
};

// 发送重置密码验证码
export const sendResetPasswordCode = async (email: string): Promise<boolean> => {
  try {
    await api.post<ResetPasswordSendRequest, ApiResponse<void>>('/reset-password/send', {
      email
    });
    return true;
  } catch (error) {
    console.error('Failed to send reset password code:', error);
    throw error;
  }
};

// 验证重置密码验证码
export const verifyResetPasswordCode = async (email: string, code: string): Promise<string> => {
  try {
    const res = await api.post<ResetPasswordVerifyRequest, ApiResponse<string>>('/reset-password/verify', {
      email,
      code
    });
    return res?.data || '';
  } catch (error) {
    console.error('Failed to verify reset password code:', error);
    throw error;
  }
};

// 重置密码
export const resetPassword = async (
  email: string,
  code: string,
  state: string,
  newPassword: string
): Promise<boolean> => {
  try {
    await api.post<ResetPasswordResetRequest, ApiResponse<void>>('/reset-password/reset', {
      email,
      code,
      state,
      newPassword
    });
    return true;
  } catch (error) {
    console.error('Failed to reset password:', error);
    throw error;
  }
};

// 更新用户偏好设置（时区或语言）
export const updateUserPreference = async (
  userId: number,
  updateType: UserPreferenceUpdateType,
  preferLanguage?: string,
  preferTimeZone?: string
): Promise<string> => {
  try {
    const res = await api.post<UserPreferSettingUpdateRequest, ApiResponse<string>>('/user/prefer-setting', {
      userId,
      updateType,
      preferLanguage,
      preferTimeZone
    });
    return res?.data;
  } catch (error) {
    throw error;
  }
};

// 获取用户信息
export const getUserInfo = async (): Promise<UserInfoResponse | null> => {
  try {
    const response = await api.get<void, ApiResponse<UserInfoResponse>>('/user/info');
    return response?.data || null;
  } catch (error) {
    console.error('Failed to fetch user info:', error);
    return null;
  }
};
