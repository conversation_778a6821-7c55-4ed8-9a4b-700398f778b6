import { RouteObject, Navigate } from 'react-router-dom';
import { Home } from './pages/agent/home';
import Login from './pages/login';
import Satisfaction from './pages/agent/Satisfaction';
import NotFound from './pages/NotFound';
import { Layout, GuardedRoute } from './pages/Layout';
import Analysis from './pages/analysis';
import { Messages } from './pages/agent/Messages';
import { Settings } from './pages/agent/Settings';
import { Auth } from '@/pages/Auth';
import { MerchantHome } from './pages/merchant/home';
import { MerchantSatisfaction } from './pages/merchant/MerchantSatisfaction';
import { MerchantReviews } from './pages/merchant/MerchantReviews';
import { MerchantMessages } from './pages/merchant/MerchantMessages';
import { MerchantSettings } from './pages/merchant/setting';
import { DynamicPageWrapper } from './pages/Layout/DynamicPageWrapper';

import { MenuCodeEnums, RouteConfig } from '@/components/components';

// 创建动态页面组件
const DynamicHome = () => <DynamicPageWrapper agentComponent={Home} merchantComponent={MerchantHome} />;

const DynamicSatisfaction = () => <DynamicPageWrapper agentComponent={Satisfaction} merchantComponent={MerchantSatisfaction} />;

const DynamicAnalysis = () => <DynamicPageWrapper agentComponent={Analysis} merchantComponent={Analysis} />;

const DynamicReviews = () => (
  <DynamicPageWrapper agentComponent={() => <div>Agent无Reviews页面</div>} merchantComponent={MerchantReviews} />
);

const DynamicMessages = () => <DynamicPageWrapper agentComponent={Messages} merchantComponent={MerchantMessages} />;

const DynamicSettings = () => <DynamicPageWrapper agentComponent={Settings} merchantComponent={MerchantSettings} />;

const DynamicAuth = () => <DynamicPageWrapper agentComponent={Auth} merchantComponent={Auth} />;

// 统一的路由配置
const baseRouteConfigs = [
  { path: '', component: DynamicHome, menuCode: MenuCodeEnums.Home, requireAuth: true, index: true },
  { path: 'satisfaction', component: DynamicSatisfaction, menuCode: MenuCodeEnums.Satisfaction, requireAuth: true },
  { path: 'analysis', component: DynamicAnalysis, menuCode: MenuCodeEnums.Analysis, requireAuth: true },
  { path: 'reviews', component: DynamicReviews, menuCode: MenuCodeEnums.Reviews, requireAuth: true },
  { path: 'messages', component: DynamicMessages, menuCode: MenuCodeEnums.Messages, requireAuth: true },
  { path: 'settings', component: DynamicSettings, menuCode: MenuCodeEnums.Settings, requireAuth: true },
  { path: 'auth', component: DynamicAuth, menuCode: MenuCodeEnums.Auth, requireAuth: true }
];

export const allRoutes: RouteConfig[] = baseRouteConfigs;

const routes: RouteObject[] = [
  {
    path: '/login',
    element: <Login />
  },
  {
    path: '/',
    element: <Layout />,
    children: [
      // 添加重定向路由
      {
        path: 'index',
        element: <Navigate to="/" replace />
      },
      // 动态生成所有路由
      ...allRoutes.map(({ component: Component, menuCode, index, path, requireAuth }) => ({
        ...(index ? { index: true } : { path }),
        element: requireAuth ? (
          <GuardedRoute requiredMenuCode={menuCode} allRoutes={allRoutes}>
            <Component />
          </GuardedRoute>
        ) : (
          <Component />
        )
      })),
      // 404路由
      {
        path: '*',
        element: <NotFound />
      }
    ]
  }
];

export default routes;
