import { useState, useEffect } from 'react';
import { getMerchantStatistics, getTodayComments } from '@/api';
import { StatisticsResponse } from '@/types';

// 商户统计数据hook
export const useMerchantStatistics = () => {
  const [data, setData] = useState<StatisticsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getMerchantStatistics();
        setData(response);
      } catch (err) {
        const errorMessage = '获取商户统计数据失败';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
};

// 当日评价数据hook
export const useTodayComments = () => {
  const [data, setData] = useState<StatisticsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getTodayComments();
        setData(response);
      } catch (err) {
        const errorMessage = '获取当日评价数据失败';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { data, loading, error };
};