import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { MenuCodeEnums, RouteConfig } from '@/components/components';

// 从需要权限的路由生成路径到菜单代码的映射
const getRestrictedPaths = (routes: RouteConfig[]) =>
(routes
  .filter(route => route.requireAuth) // 只包含需要权限的路由
  .reduce((acc, route) => {
    // 处理首页路由：path: '' 且 index: true 的情况
    if (route.path === '' && route.index) {
      acc['/'] = route.menuCode!; // requireAuth为true时menuCode必定存在
    } else {
      // 其他路由添加 '/' 前缀
      acc[`/${route.path}`] = route.menuCode!;
    }
    return acc;
  }, {} as Record<string, MenuCodeEnums>));

// 不需要权限的路由路径集合
const getPublicPaths = (routes: RouteConfig[]) => (new Set(
  routes
    .filter(route => !route.requireAuth)
    .map(route => route.path === '' && route.index ? '/' : `/${route.path}`)
))

export const useRouteGuard = (allRoutes: RouteConfig[], requiredMenuCode?: MenuCodeEnums) => {
  const { currentUser, menus, loading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    if (loading) return;

    // 如果是登录页面，不进行权限检查
    if (location.pathname === '/login') {
      return;
    }

    // 如果是公开路径，不需要权限检查
    if (getPublicPaths(allRoutes).has(location.pathname)) {
      return;
    }

    // 未登录检查
    if (!currentUser) {
      // 保持当前的 URL 参数（如 lang 参数）
      const currentSearch = location.search;
      const loginUrl = currentSearch ? `/login${currentSearch}` : '/login';
      navigate(loginUrl, { state: { from: location }, replace: true });
      return;
    }

    // 确定需要的权限
    const menuCode = requiredMenuCode || getRestrictedPaths(allRoutes)[location.pathname];
    if (!menuCode) {
      return;
    }

    // 权限检查
    const hasPermission = Array.isArray(menus) &&
      menus.some(menu => menu.menuCode === menuCode);

    if (!hasPermission) {
      const hasHomePermission = menus.some(menu => menu.menuCode === MenuCodeEnums.Home);
      if (hasHomePermission && location.pathname !== '/') {
        navigate('/', { replace: true });
      } else if (!hasHomePermission) {
        navigate('/login', { replace: true });
      }
    }
  }, [currentUser, menus, loading, location.pathname, requiredMenuCode]);

  return { isAuthorized: !loading && currentUser };
}; 