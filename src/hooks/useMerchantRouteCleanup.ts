import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useMerchant } from '@/contexts/MerchantContext';

export const useMerchantRouteCleanup = () => {
  const location = useLocation();
  const { clearMerchant } = useMerchant();

  useEffect(() => {
    // 检查当前路径是否不是merchant路由
    // 如果用户从merchant路由切换到其他路由（如agent路由），清除merchant数据
    if (!location.pathname.startsWith('/merchant/')) {
      clearMerchant();
    }
  }, [location.pathname, clearMerchant]);
}; 