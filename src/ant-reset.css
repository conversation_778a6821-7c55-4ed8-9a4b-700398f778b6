.ant-alert-error {
  border: none;
  background-color: #ffece8;
  border-radius: 2px;
}

/* 全局优化 Tabs 样式 */
.ant-tabs-tab {
  margin: 0 !important;
  border-radius: 0 !important;
}

.ant-tabs-card .ant-tabs-tab-active {
  background-color: #0d728f !important;
  color: #fff !important;
}

.ant-tabs-card .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #fff !important;
}

/* 移除 tabs 之间的间距 */
.ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab {
  margin-left: 0 !important;
}

/* 确保 card 类型的 tabs 也遵循相同规则 */
.ant-tabs-card .ant-tabs-tab {
  margin: 0 !important;
  border-radius: 0 !important;
}

.ant-tabs-card .ant-tabs-tab-active {
  background-color: #0d728f !important;
  border-color: #0d728f !important;
}

.ant-tabs-card .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #fff !important;
}

.ant-tabs-card .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #fff !important;
}

/* Pagination 选中页码样式 */
.ant-pagination .ant-pagination-item-active {
  background-color: #e4f5f5 !important;
  border-color: #e4f5f5 !important;
}

.ant-pagination .ant-pagination-item-active a {
  color: #0d728f !important;
}

/* 确保 hover 状态也使用相同颜色 */
.ant-pagination .ant-pagination-item-active:hover {
  background-color: #e4f5f5 !important;
  border-color: #e4f5f5 !important;
}

.ant-pagination .ant-pagination-item-active:hover a {
  color: #0d728f !important;
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #0d728f !important;
}

.ant-tabs-ink-bar {
  background: #0d728f !important;
}

.ant-tabs-nav .ant-tabs-tab {
  margin-right: 32px !important;
}

.ant-tabs-card .ant-tabs-nav .ant-tabs-tab {
  margin-right: 0 !important;
}

.ant-btn {
  border: none;
  border-radius: 2px;
}

.ant-btn .ant-btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ant-modal-root .ant-btn-primary {
  background-color: #0d728f !important;
  border-color: #0d728f !important;
  color: #fff;
  border: none;
  border-radius: 2px;
}

.ant-modal-root .ant-btn-default {
  background-color: #f7f8fa !important;
  color: rgba(0, 0, 0, 0.65) !important;
  border: none;
}

.ant-btn-default {
  background-color: #f7f8fa !important;
  color: rgba(0, 0, 0, 0.65) !important;
  border: none;
}

.ant-btn-primary:disabled {
  background-color: rgba(0, 0, 0, 0.25) !important;
  color: white !important;
  border: none;
}

.ant-alert {
  border: none;
  padding: 9px 16px;
}

.ant-dropdown .ant-badge-dot {
  width: 8px;
  height: 8px;
  background-color: #ca4e33;
}

.ant-modal-root .ant-badge-dot {
  width: 8px;
  height: 8px;
  background-color: #ca4e33;
}

.ant-modal-root .ant-input {
  border-radius: 4px;
}

.ant-modal-root .ant-input-outlined:focus {
  border-color: #024f69 !important;
}

.ant-modal-root .ant-input-status-success {
  border-color: #024f69 !important;
}

.ant-input {
  border-radius: 4px;
}

.ant-input-status-success {
  border-color: #024f69 !important;
}

.ant-input-outlined:focus,
.ant-input-outlined:focus-within {
  border-color: #024f69 !important;
}

/* 移除列表项的marker */
.ant-list-item {
  list-style: none;
}

/* 或者更具体地针对marker伪元素 */
.ant-list-item::marker {
  display: none;
}
