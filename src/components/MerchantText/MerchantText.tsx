import { useNavigate } from 'react-router-dom';
import { MerchantTextOrLinkProps } from '../components';
import { useMerchant } from '@/contexts/MerchantContext';
import { useAuth } from '@/contexts/AuthContext';
import { useCallback } from 'react';
import './index.css';

export const MerchantText = ({ onClick, routeJump, merchant }: MerchantTextOrLinkProps) => {
  const navigate = useNavigate();
  const { setMerchant } = useMerchant();
  const { switchRole, userContext } = useAuth();

  const onViewMerchant = useCallback(() => {
    if (onClick) {
      onClick(merchant);
    } else {
      if (routeJump) {
        switchRole(userContext?.merchantRoleId, merchant.id);
        setMerchant(merchant);
        navigate('/', { replace: true });
      }
    }
  }, []);

  return (
    <div className="merchant-text text-base" style={{ color: 'rgba(0, 0, 0, 0.88)' }} onClick={() => onViewMerchant()}>
      {merchant?.title}
    </div>
  );
};
