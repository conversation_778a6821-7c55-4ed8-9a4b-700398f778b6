import { useRef, useEffect, useState } from 'react';
import { Tooltip } from 'antd';

export const TextEllipsis = ({
  text,
  className,
  maxWidth,
  width,
  line = 1
}: {
  text: string;
  className?: string;
  maxWidth?: string;
  width?: string;
  line?: number;
}) => {
  const textRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

  useEffect(() => {
    const checkOverflow = () => {
      if (textRef.current) {
        const element = textRef.current;
        if (line === 1) {
          // 单行省略的溢出检查
          const isTextOverflowing = element.scrollWidth > element.clientWidth;
          setIsOverflowing(isTextOverflowing);
        } else {
          // 多行省略的溢出检查
          const isTextOverflowing = element.scrollHeight > element.clientHeight;
          setIsOverflowing(isTextOverflowing);
        }
      }
    };

    // Check overflow on mount and when text changes
    checkOverflow();

    // Optional: Check on window resize
    const handleResize = () => checkOverflow();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [text, maxWidth, line]);

  const textElement = (
    <div
      ref={textRef}
      className={`overflow-hidden ${className}`}
      style={{
        maxWidth,
        width,
        ...(line === 1
          ? {
              // 单行省略
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis'
            }
          : {
              // 多行省略
              display: '-webkit-box',
              WebkitLineClamp: line,
              WebkitBoxOrient: 'vertical',
              wordBreak: 'break-word'
            })
      }}
    >
      {text}
    </div>
  );

  // Only wrap with Tooltip if text is overflowing
  if (isOverflowing) {
    return (
      <Tooltip title={text} placement="top">
        {textElement}
      </Tooltip>
    );
  }

  return textElement;
};
