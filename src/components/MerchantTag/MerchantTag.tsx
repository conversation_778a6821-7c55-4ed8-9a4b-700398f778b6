import { useAuth } from '@/contexts/AuthContext';
import { useMerchant } from '@/contexts/MerchantContext';
import { useNavigate } from 'react-router-dom';
import { TextEllipsis } from '../TextEllipsis';
import { MerchantTextOrLinkProps } from '../components';

export const MerchantTag = ({ merchant, routeJump: routeJump, onClick, ...props }: MerchantTextOrLinkProps) => {
  const navigate = useNavigate();
  const { setMerchant } = useMerchant();
  const { switchRole, userContext } = useAuth();

  const onViewMerchant = () => {
    if (onClick) {
      onClick();
    } else {
      if (routeJump) {
        switchRole(userContext?.merchantRoleId, merchant.id);
        setMerchant(merchant);
        navigate('/', { replace: true });
      }
    }
  };

  return (
    <div
      className="bg-[#F7F8FA] py-[6px] px-2 rounded text-start text-sm cursor-pointer transition-all duration-200 w-[150px] h-min-8 block"
      onClick={onViewMerchant}
      onMouseEnter={e => {
        e.currentTarget.style.backgroundColor = '#e6f7ff';
      }}
      onMouseLeave={e => {
        e.currentTarget.style.backgroundColor = '#F7F8FA';
      }}
      key={merchant.id}
      {...props}
    >
      <TextEllipsis text={merchant?.title} />
    </div>
  );
};
