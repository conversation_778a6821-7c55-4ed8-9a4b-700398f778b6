// 菜单代码枚举

import { Merchant } from "@/types"

export enum MenuType {
  Agent = 'agent',
  Merchant = 'merchant'
}

export const HomeMenu = 'Home'
export const IndexMenu = 'Index'
export const SatisfactionMenu = 'Satisfaction'
export const AnalysisMenu = 'Analysis'
export const ReviewsMenu = 'Reviews'
export const MessagesMenu = 'Messages'
export const SettingsMenu = 'Settings'
export const AuthMenu = 'Auth'

export const MenuTypeConstants = {
  Home: HomeMenu,
  Index: IndexMenu,
  Satisfaction: SatisfactionMenu,
  Analysis: AnalysisMenu,
  Reviews: ReviewsMenu,
  Messages: MessagesMenu,
  Settings: SettingsMenu,
  Auth: AuthMenu
}

export enum MenuCodeEnums {
  Home = '1',
  Satisfaction = '2',
  Analysis = '3',
  Reviews = '4',
  Messages = '5',
  Settings = '6',
  Auth = '7'
}

export const AgentMenuCodeEnumsMap = {
  [HomeMenu]: MenuCodeEnums.Home,
  [SatisfactionMenu]: MenuCodeEnums.Satisfaction,
  [AnalysisMenu]: MenuCodeEnums.Analysis,
  [ReviewsMenu]: MenuCodeEnums.Reviews,
  [MessagesMenu]: MenuCodeEnums.Messages,
  [SettingsMenu]: MenuCodeEnums.Settings,
  [AuthMenu]: MenuCodeEnums.Auth
}

export const MerchantMenuCodeEnumsMap = {
  [HomeMenu]: MenuCodeEnums.Home,
  [SatisfactionMenu]: MenuCodeEnums.Satisfaction,
  [AnalysisMenu]: MenuCodeEnums.Analysis,
  [ReviewsMenu]: MenuCodeEnums.Reviews,
  [MessagesMenu]: MenuCodeEnums.Messages,
  [SettingsMenu]: MenuCodeEnums.Settings,
  [AuthMenu]: MenuCodeEnums.Auth
}

// 菜单项配置
export interface MenuItemConfig {
  key: string;
  menuCode: MenuCodeEnums;
  icon: React.ReactNode;
  label: React.ReactNode;
  requireAuth?: boolean;
}

export interface RouteConfig {
  path: string;
  index?: boolean;
  component: React.ComponentType;
  menuCode?: MenuCodeEnums;
  requireAuth?: boolean;
}


export interface MerchantTextOrLinkProps {
  merchant: Merchant;
  routeJump?: boolean;
  // 自定义click事件
  onClick?: (merchant: Merchant) => void;
}