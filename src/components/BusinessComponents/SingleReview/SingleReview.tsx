import { List, Avatar, Button, Input, Space, message, Rate, Typography, Form, Modal } from 'antd';
import { GetGoogleReviewsResponseContent } from '@/types/review';
import { CSSProperties, useCallback, useState } from 'react';
import { formatDate } from '@/utils/utils';
import ReplySvg from '@/assets/svg/reply.svg';
import EditSvg from '@/assets/svg/edit.svg';
import AiSvg from '@/assets/svg/ai.svg?react';
import WarningSvg from '@/assets/svg/warning-green.svg?react';
import { getAIReply, replyGoogleReview } from '@/api/review';
import { FunctionButton } from '@/components/FunctionButton';
import { useMerchant } from '@/contexts/MerchantContext';
import { useTranslation } from 'react-i18next';
import './index.css';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

type Review = Omit<GetGoogleReviewsResponseContent, 'starRating'> & { merchantName?: string; starRating?: number };

export const SingleReview = ({
  review,
  refresh,
  showAvatar = true,
  replyStyle = {}
}: {
  review: Review;
  refresh: () => void;
  showAvatar?: boolean;
  replyStyle?: CSSProperties;
}) => {
  const { t } = useTranslation();
  const {
    id,
    evaluateName,
    evaluateUrl,
    starRating,
    content,
    anonymousFlag,
    commentUpdateTime,
    replyContent,
    replyUpdateTime,
    aiTag,
    merchantName
  } = review;

  const { merchant } = useMerchant();

  const [form] = Form.useForm();
  const [replyVisible, setReplyVisible] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [aiGeneratedContent, setAiGeneratedContent] = useState<string>('');
  const [loadingAiReply, setLoadingAiReply] = useState<boolean>(false);
  const [showOverwriteModal, setShowOverwriteModal] = useState<boolean>(false);

  // 生成AI回复的具体逻辑
  const generateAIReply = useCallback(async () => {
    setLoadingAiReply(true);

    try {
      if (content) {
        const aiReply = await getAIReply(id.toString());
        if (aiReply) {
          setAiGeneratedContent(aiReply);
          form.setFieldsValue({
            replyContent: aiReply
          });
          message.success('AI回复生成成功');
        }
      } else {
        message.warning('评论内容为空，无法生成AI回复');
      }
    } finally {
      setLoadingAiReply(false);
    }
  }, [content, id, form]);

  // 点击回复按钮 - 只显示回复表单，不自动生成AI回复
  const handleReply = useCallback(() => {
    setReplyVisible(true);
    form.setFieldsValue({
      replyContent: ''
    });
  }, [form]);

  // 编辑回复 - 将现有回复内容填入表单
  const handleEditReply = (existingComment: string) => {
    setReplyVisible(true);

    form.setFieldsValue({
      replyContent: existingComment
    });
  };

  // AI自动生成回复
  const handleGenerateAIReply = async () => {
    const currentContent = form.getFieldValue('replyContent') || '';

    // 如果已有内容，显示覆盖确认对话框
    if (currentContent.trim()) {
      setShowOverwriteModal(true);
      return;
    }

    // 直接生成AI回复
    await generateAIReply();
  };

  // 确认覆盖后生成AI回复
  const handleConfirmOverwrite = async () => {
    setShowOverwriteModal(false);
    await generateAIReply();
  };

  // 提交回复 - 调用 replyGoogleReview 接口
  const handleSubmitReply = async () => {
    const data = await form.validateFields();
    setSubmitting(true);
    try {
      // 判断当前内容是否与AI生成的内容相同
      const isAiContent = !!aiGeneratedContent && data.replyContent === aiGeneratedContent;

      // 调用真实的 replyGoogleReview 接口
      const success = await replyGoogleReview({
        id: id,
        replyContent: data.replyContent,
        aiTag: isAiContent as boolean
      });

      if (success) {
        message.success(t('review.replySuccess'));
        setReplyVisible(false);
        form.resetFields();
        setAiGeneratedContent('');
        refresh();
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 取消回复
  const handleCancelReply = useCallback(() => {
    form.resetFields();
    setReplyVisible(false);
    setLoadingAiReply(false);
    setAiGeneratedContent('');
  }, [form]);

  return (
    <List.Item key={id}>
      <div className="p-4 border-[1px] rounded">
        <List.Item.Meta
          avatar={
            showAvatar && (
              <Avatar
                style={{ backgroundColor: '#d9d9d9' }}
                icon={!anonymousFlag && evaluateUrl && <img src={evaluateUrl} />}
                size="large"
              />
            )
          }
          title={
            <Space size="middle" className="flex justify-between items-center h-10">
              {evaluateName && <Text strong>{evaluateName}</Text>}
              <Rate value={starRating || 0} disabled />
            </Space>
          }
        />
        <Paragraph className="mt-3 mb-4">{content}</Paragraph>
        <Paragraph className="flex justify-end items-center">{`${t('review.editTime')} ${formatDate(commentUpdateTime)}`}</Paragraph>
        <Paragraph className="flex justify-end items-center">
          <FunctionButton
            text={t('review.reply')}
            onClick={() => handleReply()}
            icon={<img src={ReplySvg} alt="reply" className="w-4 h-4" />}
          />
        </Paragraph>
      </div>

      {/* 回复显示 */}
      {replyContent && (
        <div className="p-4 pl-20 bg-[#F7F8FA] rounded flex flex-col gap-2 justify-start items-start" style={replyStyle}>
          <div className="flex w-full justify-between items-center text-sm  mb-2 " style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
            <span className="text-base font-bold">
              {merchantName || merchant?.title} {t('common.merchant')}
            </span>
            {aiTag && <span className="text-blue-500 bg-[#B3E5E8] py-1 px-2 color-[#0D728F] rounded-[2px]">AI</span>}
          </div>
          <Paragraph className="text-md">{replyContent}</Paragraph>
          <Paragraph className="flex justify-between items-center w-full" style={{ color: 'rgba(0, 0, 0, 0.651)' }}>
            <span>{`${t('review.lastEditTime')} ${formatDate(replyUpdateTime)}`}</span>
            <FunctionButton
              text={t('review.edit')}
              onClick={() => handleEditReply(replyContent)}
              icon={<img src={EditSvg} alt="edit" className="w-4 h-4" />}
            />
          </Paragraph>
        </div>
      )}

      {/* 回复表单 */}
      {replyVisible && (
        <div className="mt-2">
          <div className="flex justify-between items-center">
            <div className="text-base font-bold">{t('review.replyComment')}</div>
            <Button
              type="text"
              size="small"
              loading={loadingAiReply}
              onClick={handleGenerateAIReply}
              className="flex items-center flex-row justify-between gap-1 bg-[#F7F8FA] rounded-[2px]"
              style={{
                color: 'rgba(0, 0, 0, 0.65)',
                padding: '4px 16px'
              }}
              icon={<AiSvg className="w-[13px]" />}
            >
              自动生成
            </Button>
          </div>
          <Form form={form}>
            <div className="mt-4 p-4 border border-gray-200 rounded">
              <Form.Item name="replyContent" rules={[{ required: true, message: t('review.pleaseEnterReplyContent') }]}>
                <TextArea placeholder={t('review.pleaseEnterReplyContent')} className="mb-3 !h-[278px]" disabled={loadingAiReply} />
              </Form.Item>
              <Space size="middle">
                <Button loading={submitting} onClick={handleCancelReply}>
                  {t('common.cancel')}
                </Button>
                <Button type="primary" loading={submitting} onClick={handleSubmitReply}>
                  {t('common.submit')}
                </Button>
              </Space>
            </div>
          </Form>
        </div>
      )}

      {/* 覆盖确认对话框 */}
      <Modal
        title={
          <div className="flex items-center">
            <WarningSvg className="w-6 h-6 mr-3" />
            <span className="font-bold">是否覆盖原有内容</span>
          </div>
        }
        open={showOverwriteModal}
        onOk={handleConfirmOverwrite}
        onCancel={() => setShowOverwriteModal(false)}
        okText="覆盖"
        cancelText={t('common.cancel')}
        // okButtonProps={{ style: { backgroundColor: '#0D728F', borderColor: '#0D728F' } }}
      >
        <div className="mt-4 mb-1">确认要覆盖原有的评论内容</div>
      </Modal>
    </List.Item>
  );
};
