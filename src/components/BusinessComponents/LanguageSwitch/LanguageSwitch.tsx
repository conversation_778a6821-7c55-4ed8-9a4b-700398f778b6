import React, { useEffect, useState } from 'react';
import { Dropdown, Button, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { GlobalOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import DropdownIcon from '@/assets/svg/dropdown.svg?react';
import { getUserInfo, updateUserPreference } from '@/api/user';
import { UserPreferenceUpdateType, LanguageCode } from '@/types';
import { useAuth } from '@/contexts/AuthContext';

interface LanguageConfig {
  code: LanguageCode;
  name: string;
}

interface LanguageSwitcherProps {
  style?: React.CSSProperties;
  hideIcon?: boolean;
  initialLanguage?: LanguageCode;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ style = {}, hideIcon = false, initialLanguage }) => {
  const { i18n, t } = useTranslation();
  const { currentUser, setTemporaryLanguage } = useAuth();
  const [open, setOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>(LanguageCode.EN_US);

  useEffect(() => {
    i18n.changeLanguage(currentLanguage);
  }, [currentLanguage]);

  useEffect(() => {
    if (initialLanguage) {
      setCurrentLanguage(initialLanguage);
    }
  }, [initialLanguage]);

  const languages: LanguageConfig[] = [
    { code: LanguageCode.ZH_CN, name: t('language.zhCN') },
    { code: LanguageCode.ZH_TW, name: t('language.zhTW') },
    { code: LanguageCode.EN_US, name: t('language.enUS') },
    { code: LanguageCode.JA_JP, name: t('language.jaJP') }
  ];

  useEffect(() => {
    const fetchUserInfo = async () => {
      if (!currentUser?.userId) return;

      try {
        const userInfo = await getUserInfo();
        if (userInfo?.preferLanguage) {
          const userLanguage = userInfo.preferLanguage as LanguageCode;
          setCurrentLanguage(userLanguage);
        }
      } catch (error) {
        console.error('Failed to fetch user info:', error);
      }
    };

    fetchUserInfo();
  }, [currentUser?.userId]);

  const handleLanguageChange = async (languageCode: LanguageCode) => {
    try {
      // 如果有用户信息，调用接口更新用户偏好设置
      if (currentUser?.userId) {
        const result = await updateUserPreference(currentUser.userId, UserPreferenceUpdateType.LANGUAGE, languageCode);
        setCurrentLanguage(languageCode);
        if (result) {
          message.success(result);
        }
      } else {
        setTemporaryLanguage(languageCode);
        setCurrentLanguage(languageCode);
      }

      setOpen(false);
    } catch (error) {
      console.error('Failed to update user language preference:', error);
      // 接口调用失败，不切换本地语言，保持原来的设置
      setOpen(false);
    }
  };

  const menuItems: MenuProps['items'] = languages.map(lang => ({
    key: lang.code,
    label: (
      <div className="flex items-center gap-2 py-1">
        <span>{lang.name}</span>
      </div>
    ),
    onClick: () => handleLanguageChange(lang.code)
  }));

  return (
    <Dropdown
      menu={{ items: menuItems }}
      open={open}
      onOpenChange={setOpen}
      placement="bottomRight"
      trigger={['click']}
      overlayStyle={{ zIndex: 9999 }}
    >
      <Button
        className="flex items-center gap-2"
        style={{
          color: 'white',
          height: '32px',
          padding: '0 12px',
          ...style
        }}
        size="small"
        type="text"
      >
        {!hideIcon && <GlobalOutlined />}
        <span>{languages.find(lang => lang.code === currentLanguage)?.name}</span>
        <DropdownIcon style={{ width: '12px', height: '12px', transform: open ? 'rotate(180deg)' : 'rotate(0deg)' }} />
      </Button>
    </Dropdown>
  );
};
