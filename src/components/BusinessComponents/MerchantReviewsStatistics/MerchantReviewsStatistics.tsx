import { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { DatePicker, Tabs } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import ReactECharts from 'echarts-for-react';
import { getTrendColor, getTriangleStyle } from '@/utils/utils';
import { getAllComments } from '@/api/dashboard';
import { PeriodType } from '@/types/review';
import './index.css';

const { RangePicker } = DatePicker;

const getTimeOptions = (t: any) => [
  { label: t('time.currentDay'), value: PeriodType.TODAY },
  { label: t('time.last7Days'), value: PeriodType.PAST_7_DAYS },
  { label: t('time.last30Days'), value: PeriodType.PAST_30_DAYS }
];

const getDefaultRange = (type: string): [Dayjs, Dayjs] => {
  if (type === PeriodType.TODAY) return [dayjs().startOf('day'), dayjs().endOf('day')];
  if (type === PeriodType.PAST_7_DAYS) return [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')];
  if (type === PeriodType.PAST_30_DAYS) return [dayjs().subtract(29, 'day').startOf('day'), dayjs().endOf('day')];
  // 默认近7天
  return [dayjs().startOf('day'), dayjs().endOf('day')];
};

export const MerchantReviewsStatistics = ({
  title,
  subTitle,
  compareText,
  timeChange = true,
  merchantId,
  defaultRange = PeriodType.TODAY
}: {
  title: string;
  subTitle: string;
  compareText: string;
  timeChange: boolean;
  merchantId?: number;
  defaultRange?: PeriodType;
}) => {
  const { t } = useTranslation();
  // tabKey: 'today' | 'week' | 'month' | null
  const [tabKey, setTabKey] = useState<PeriodType | undefined>(defaultRange);
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>(getDefaultRange(defaultRange));
  const [data, setData] = useState<any>(null);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setDateRange(getDefaultRange(defaultRange));
  }, []);

  // 切换Tab时，自动更新日期
  const handleTabChange = useCallback((key: PeriodType) => {
    setTabKey(key);
    const range = getDefaultRange(key);
    setDateRange(range);
  }, []);

  // 日期变化时，自动取消Tab高亮
  const handleRangeChange = useCallback((dates?: [Dayjs, Dayjs]) => {
    if (dates) {
      setDateRange(dates);
      setTabKey(undefined); // 取消tab高亮
    }
  }, []);

  // 禁用今天之后的日期
  const disabledDate = useCallback((current: Dayjs) => {
    // 禁用今天之后的日期
    return current && current.isAfter(dayjs().endOf('day'));
  }, []);

  // 请求数据
  useEffect(() => {
    const fetchData = async () => {
      if (!dateRange) return;
      const [start, end] = dateRange;
      const res = await getAllComments({
        startDate: start.format('YYYY-MM-DDTHH:mm:ss'),
        endDate: end.format('YYYY-MM-DDTHH:mm:ss'),
        merchantId,
        type: tabKey ? 'select' : 'custom'
      });
      setData(res);
    };
    fetchData();
  }, [dateRange, merchantId]);

  // ECharts配置替换原来的barConfig
  const chartOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>${data.marker}${t('home.reviewCount')}: ${data.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [...(data?.allComments ?? []).map((item: any) => item.merchantTypeName)],
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(0, 0, 0, 0.88)',
        fontSize: 14,
        fontWeight: 500,
        interval: 0, // 显示所有标签
        rotate: 0 // 不旋转标签
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: (value: any) => {
        // 确保最大值至少为10
        return Math.max(Math.ceil(value.max / 10) * 10, 10);
      },
      minInterval: 1, // 设置最小刻度间隔为1
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        formatter: (value: number) => `${value}`
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          width: 1,
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: [...(data?.allComments ?? []).map((item: any) => item.totalComments)],
        itemStyle: {
          color: '#0D728F',
          borderRadius: [2, 2, 0, 0] // 顶部圆角
        },
        barMaxWidth: 100, // 设置最大宽度为100像素
        emphasis: {
          itemStyle: {
            color: '#0A5F75' // 悬停时的颜色
          }
        }
      }
    ]
  };

  const isValidComparedData = data?.comparedToYesterday && data?.comparedToYesterday !== '--';

  return (
    <div style={{ background: '#fff', borderRadius: 8, padding: 24 }}>
      {/* 右上角操作区 */}
      <div className="flex flex-row justify-between items-center">
        <div style={{ fontSize: 22, fontWeight: 600, marginBottom: 24 }}>{title}</div>
        {timeChange && (
          <div className="flex flex-row justify-end items-center gap-4">
            <Tabs
              className="home-reviews-all-tabs"
              activeKey={tabKey}
              onChange={key => handleTabChange(key as PeriodType)}
              items={getTimeOptions(t).map(({ label, value }) => ({ key: value, label }))}
              size="small"
              type="card"
            />
            <RangePicker
              value={dateRange}
              onChange={dates => handleRangeChange(dates as [Dayjs, Dayjs])}
              allowClear={false}
              disabledDate={disabledDate}
            />
          </div>
        )}
      </div>
      {/* 内容区 横向布局 */}
      <div ref={ref} className="flex flex-row items-start gap-6 min-w-0 overflow-hidden">
        {/* 左侧统计文字 */}
        <div style={{ width: '180px', flexShrink: 0 }}>
          <div className="text-base" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
            {subTitle}
          </div>
          <div className="flex flex-row items-end justify-start gap-[10px] -mt-3">
            <div className="text-[60px] font-bold text-[#1D1D1D] flex flex-col justify-end -mb-[14px]">{data?.toDayComments ?? '-'}</div>
            <div
              style={{
                color: isValidComparedData ? getTrendColor(Number(data?.comparedToYesterday)) : '',
                fontSize: 16
              }}
            >
              {isValidComparedData ? `${data?.comparedToYesterday}%` : '--'}
              {isValidComparedData && <span style={getTriangleStyle(Number(data?.comparedToYesterday))} className="ml-1" />}
              <div style={{ color: 'rgba(29, 29, 29, 0.6)', fontSize: 14 }}>{compareText}</div>
            </div>
          </div>
          <div className="mt-10">
            <div className="mt-10" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
              {t('home.weekGoodRate')}
            </div>
            <div style={{ fontSize: 36, fontWeight: 700 }}>{data?.weekGood ? `${data.weekGood}%` : '-'}</div>
          </div>
        </div>
        {/* 右侧柱状图 */}
        <div className="flex-1 min-w-0 overflow-hidden">
          <ReactECharts option={chartOption} style={{ height: 260, width: '100%' }} opts={{ renderer: 'canvas' }} />
        </div>
      </div>
    </div>
  );
};
