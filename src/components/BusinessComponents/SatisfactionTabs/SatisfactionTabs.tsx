import React, { useMemo, useState } from 'react';
import { Tabs, Card, Row, Col } from 'antd';
import ReactECharts from 'echarts-for-react';
import './index.css';
import { RadarChartDataResponse, CustomerSatisfactionData, PlatformType, CustomerType, DimensionType } from '@/types/api';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

const formatUpdateTime = (tFun: (key: string) => string, time?: string) => {
  if (!time) return '';
  const d = dayjs(time);
  if (d.isToday()) return `${tFun('time.today')} ${d.format('HH:mm')}${tFun('time.updated')}`;
  if (d.isYesterday()) return `${tFun('time.yesterday')} ${d.format('HH:mm')}${tFun('time.updated')}`;
  return d.format('YYYY-MM-DD HH:mm') + tFun('time.updated');
};

const DIMENSIONS = [
  {
    label: 'Food',
    key: DimensionType.Food
  },
  { label: 'Service', key: DimensionType.Service },
  { label: 'Delivery', key: DimensionType.Delivery },
  { label: 'Environment', key: DimensionType.Environment },
  { label: 'Safety & Cleanliness', key: DimensionType.SafetyCleanliness },
  { label: 'Price/Value', key: DimensionType.PriceValue },
  { label: 'Dietary Info', key: DimensionType.DietaryInfo }
];

const getAllPlatforms = (t: (key: string) => string) => [
  { key: PlatformType.AllPlatforms, label: t('satisfaction.ALL_PLATFORMS'), platform: PlatformType.AllPlatforms },
  { key: PlatformType.Google, label: t('satisfaction.GOOGLE'), platform: PlatformType.Google },
  { key: PlatformType.Yelp, label: t('satisfaction.YELP'), platform: PlatformType.Yelp },
  { key: PlatformType.Xiaohongshu, label: t('satisfaction.XIAOHONGSHU'), platform: PlatformType.Xiaohongshu }
];

interface CustomerData {
  allCustomers?: {
    [key in PlatformType]?: CustomerSatisfactionData;
  };
  returningCustomers?: {
    [key in PlatformType]?: CustomerSatisfactionData;
  };
}

export const SatisfactionTabs: React.FC<{ dataSource?: RadarChartDataResponse }> = ({ dataSource }) => {
  const { t } = useTranslation();
  const [tab, setTab] = useState<CustomerType>(CustomerType.AllCustomers);

  const [allCustomersData, returningCustomersData] = useMemo(() => {
    if (!dataSource || Object.keys(dataSource).length === 0) return [];
    const all: CustomerData['allCustomers'] = {};
    const returning: CustomerData['returningCustomers'] = {};
    Object.entries(dataSource).forEach(([platform, data]) => {
      if (data?.allCustomers && data.allCustomers) {
        all[platform as PlatformType] = { ...data.allCustomers, lastUpdateTime: data.lastUpdateTime };
      }
      if (data?.returningCustomers && data.returningCustomers) {
        returning[platform as PlatformType] = { ...data.returningCustomers, lastUpdateTime: data.lastUpdateTime };
      }
    });
    return [all, returning];
  }, [dataSource]);

  const customerDataSource = useMemo(() => {
    return (tab === CustomerType.AllCustomers ? allCustomersData : returningCustomersData) ?? {};
  }, [tab, allCustomersData, returningCustomersData]);

  const getOnePlatformData = (platform: PlatformType) => {
    return customerDataSource?.[platform] ?? { lastUpdateTime: '', dimensions: [] };
  };

  // 生成 ECharts option
  const radarConfig = (platform: PlatformType) => ({
    tooltip: {},
    radar: {
      indicator: DIMENSIONS.map((dimension, index) => ({
        name: dimension.label,
        max: 5,
        axisLabel: {
          show: index === 0
        }
      })),
      splitLine: {
        lineStyle: {
          color: '#eee',
          type: 'solid'
        }
      },
      splitArea: {
        areaStyle: {
          opacity: 0
        }
      },
      axisLine: {
        lineStyle: {
          color: '#eee',
          type: 'solid'
        }
      },
      axisName: {
        color: '#888',
        fontSize: 14
      },
      axisLabel: {
        show: true,
        margin: -1,
        width: 20,
        height: 13,
        backgroundColor: '#F7F8FA',
        borderRadius: 4,
        lineHeight: 13,
        align: 'center',
        color: 'rgba(0, 0, 0, 0.88)',
        showMinLabel: false
      },
      splitNumber: 5,
      startAngle: 90
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: DIMENSIONS.map(
              d => getOnePlatformData(platform)?.dimensions?.find(dimension => dimension.dimensionName === d.key)?.satisfactionScore
            ),
            name: t('satisfaction.customerSatisfaction'),
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              color: '#0D728F',
              width: 2,
              type: 'solid',
              cap: 'butt',
              join: 'miter'
            },
            itemStyle: {
              color: '#0D728F',
              borderColor: '#0D728F',
              borderWidth: 2
            },
            areaStyle: {
              opacity: 0
            }
          }
        ]
      }
    ]
  });

  return (
    <>
      <Tabs
        className="custom-satisfaction-tabs"
        activeKey={tab}
        onChange={setTab as any}
        items={[
          { key: CustomerType.AllCustomers, label: t('satisfaction.allCustomersSatisfaction') },
          { key: CustomerType.ReturningCustomers, label: t('satisfaction.returningCustomersSatisfaction') }
        ]}
      />
      <Row gutter={[16, 16]}>
        {getAllPlatforms(t).map(platform => {
          const { lastUpdateTime, dimensions } = getOnePlatformData(platform.key);

          return (
            <Col span={12} key={platform.key}>
              <Card style={{ minHeight: 360 }} className="satisfaction-card">
                <div className="text-lg font-medium text-gray-800  h-[24px]">{platform.label}</div>
                <div className="text-sm text-[13px] flex flex-row text-gray-500 justify-between items-center line-height-[20px] mb-5 mt-1">
                  <div>{t('satisfaction.last90DaysData')}</div>
                  <span>{formatUpdateTime(t, lastUpdateTime)}</span>
                </div>
                {dimensions && dimensions.length > 0 ? (
                  <ReactECharts option={radarConfig(platform.key)} style={{ height: 260, width: '100%' }} />
                ) : (
                  <div style={{ height: 260, display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#bbb' }}>
                    {t('common.noData')}
                  </div>
                )}
                <div className="text-sm text-[12px]  flex justify-center items-center gap-1 h-5">
                  <span className="w-2 h-2 bg-[#0D728F] rounded-full" />
                  <span>{t('satisfaction.satisfaction')}</span>
                </div>
              </Card>
            </Col>
          );
        })}
      </Row>
    </>
  );
};
