import { PeriodType } from '@/types';
import { Tabs } from 'antd';
import { useTranslation } from 'react-i18next';

type SectionContainerProps = {
  children: React.ReactNode;
  title: string;
  activeTabKey: string;
  onTabChange: (key: string) => void;
};

export const SectionContainer = ({ title, children, activeTabKey, onTabChange }: SectionContainerProps) => {
  const { t } = useTranslation();

  const timeOptions = [
    { label: t('time.last7Days'), value: PeriodType.WEEK },
    { label: t('time.last30Days'), value: PeriodType.MONTH },
    { label: t('time.last90Days'), value: PeriodType.THREE_MONTHS }
  ];

  return (
    <div className="flex flex-col justify-start items-start p-5 gap-5 bg-white mb-4">
      <div className="text-base font-bold" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
        {title}
      </div>
      <Tabs
        activeKey={activeTabKey}
        onChange={onTabChange}
        items={timeOptions.map(opt => ({ key: opt.value, label: opt.label }))}
        size="small"
        type="card"
        className="chart-tabs"
      />
      {children}
    </div>
  );
};
