import { useState, useMemo, useEffect } from 'react';
import { Spin } from 'antd';
import ReactECharts from 'echarts-for-react';
import { getReviewStatistics } from '@/api/review';
import { ReviewStatisticsResponse, PeriodType, DailyReviewStatistics, LowerCasePlatformEnums } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { SectionContainer } from './SectionContainer';
import { useTranslation } from 'react-i18next';

// 平台颜色映射
const PLATFORM_COLOR_MAP: Record<string, string> = {
  [LowerCasePlatformEnums.google]: '#0D728F',
  [LowerCasePlatformEnums.xiaohongshu]: '#1E6812',
  [LowerCasePlatformEnums.yelp]: '#BC912B',
  [LowerCasePlatformEnums.ig]: '#9CB62B',
  [LowerCasePlatformEnums.facebook]: '#1597B4',
  [LowerCasePlatformEnums.tiktok]: '#C25767'
};

export const AnalysisCharts = () => {
  const { t } = useTranslation();
  const { merchantId, agentId } = useAuth();
  const [platformTimeKey, setPlatformTimeKey] = useState<PeriodType>(PeriodType.THREE_MONTHS);
  const [reviewTimeKey, setReviewTimeKey] = useState<PeriodType>(PeriodType.THREE_MONTHS);
  const [platformData, setPlatformData] = useState<ReviewStatisticsResponse | null>(null);
  const [reviewData, setReviewData] = useState<ReviewStatisticsResponse | null>(null);
  const [platformLoading, setPlatformLoading] = useState(false);
  const [reviewLoading, setReviewLoading] = useState(false);

  // 获取平台数据
  const fetchPlatformData = async (periodType: PeriodType) => {
    setPlatformLoading(true);
    try {
      const response = await getReviewStatistics({ periodType, merchantId, agentId });
      setPlatformData(response);
    } finally {
      setPlatformLoading(false);
    }
  };

  // 获取评论数据
  const fetchReviewData = async (periodType: PeriodType) => {
    setReviewLoading(true);
    try {
      const response = await getReviewStatistics({ periodType, merchantId, agentId });
      setReviewData(response);
    } finally {
      setReviewLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchPlatformData(platformTimeKey);
  }, [platformTimeKey, merchantId]);

  useEffect(() => {
    fetchReviewData(reviewTimeKey);
  }, [reviewTimeKey, merchantId]);

  // 处理平台时间选择变化
  const handlePlatformTimeChange = (key: string) => {
    const periodType = key as PeriodType;
    setPlatformTimeKey(periodType);
  };

  // 处理评论时间选择变化
  const handleReviewTimeChange = (key: string) => {
    const periodType = key as PeriodType;
    setReviewTimeKey(periodType);
  };

  // 各平台评论数量分析图表配置
  const platformChartOption = useMemo(() => {
    if (!platformData?.statistics) return {};

    const platforms = Object.keys(platformData.statistics);
    if (platforms.length === 0) return {};

    // 获取所有日期（从第一个有数据的平台）
    const firstPlatform = platforms[0];
    const allDates = platformData.statistics[firstPlatform]?.dailyStatistics || [];
    const categories = allDates.map(item => {
      const date = new Date(item.date);
      return `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
    });

    const series = platforms.map(platform => {
      const platformStats = platformData.statistics[platform];
      const data = platformStats.dailyStatistics.map(daily => daily.totalCount);

      return {
        name: platform,
        type: 'line',
        data,
        smooth: false,
        lineStyle: {
          width: 2,
          color: PLATFORM_COLOR_MAP[platform]
        },
        itemStyle: {
          color: PLATFORM_COLOR_MAP[platform]
        },
        symbol: 'circle',
        symbolSize: 4,
        emphasis: {
          focus: 'series'
        }
      };
    });

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: function (params: any) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: platforms.map(platform => ({
          name: platform,
          icon: 'rect' // 使用矩形图标，这样看起来更像线条
        })),
        itemHeight: 3,
        top: 10,
        right: 20,
        orient: 'horizontal',
        itemWidth: 10,
        textStyle: {
          fontSize: 12,
          color: 'rgba(0, 0, 0, 0.65)'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: categories,
        axisLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        },
        axisLabel: {
          color: 'rgba(0, 0, 0, 0.65)',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        name: '评论数量',
        nameTextStyle: {
          color: 'rgba(0, 0, 0, 0.45)',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(0, 0, 0, 0.65)',
          fontSize: 12,
          formatter: '{value}'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      series
    };
  }, [platformData]);

  // 评价总体分析图表配置
  const reviewChartOption = useMemo(() => {
    if (!reviewData?.statistics) return {};

    const platforms = Object.keys(reviewData.statistics);
    if (platforms.length === 0) return {};

    // 合并所有平台的每日数据
    const dailyDataMap = new Map<string, DailyReviewStatistics>();

    platforms.forEach(platform => {
      const platformStats = reviewData.statistics[platform];
      platformStats.dailyStatistics.forEach(daily => {
        const existingData = dailyDataMap.get(daily.date);
        if (existingData) {
          // 累加数据
          existingData.positiveCount += daily.positiveCount;
          existingData.negativeCount += daily.negativeCount;
          existingData.totalCount += daily.totalCount;
        } else {
          // 新增数据
          dailyDataMap.set(daily.date, { ...daily });
        }
      });
    });

    // 按日期排序
    const sortedDailyData = Array.from(dailyDataMap.values()).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    const categories = sortedDailyData.map(item => {
      const date = new Date(item.date);
      return `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
    });
    const positiveData = sortedDailyData.map(item => item.positiveCount);
    const negativeData = sortedDailyData.map(item => item.negativeCount);

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: ['好评', '差评'],
        top: 10,
        right: 20,
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          fontSize: 12,
          color: 'rgba(0, 0, 0, 0.65)'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        },
        axisLabel: {
          color: 'rgba(0, 0, 0, 0.65)',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        name: '评论数量',
        nameTextStyle: {
          color: 'rgba(0, 0, 0, 0.45)',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(0, 0, 0, 0.65)',
          fontSize: 12,
          formatter: '{value}'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '好评',
          type: 'bar',
          stack: 'total',
          data: positiveData,
          itemStyle: {
            color: '#0D728F'
          }
        },
        {
          name: '差评',
          type: 'bar',
          stack: 'total',
          data: negativeData,
          itemStyle: {
            color: '#C9A94F'
          }
        }
      ]
    };
  }, [reviewData]);

  return (
    <>
      {/* 各平台评论数量分析 */}
      <SectionContainer
        title={t('analysis.platformReviewCountAnalysis')}
        activeTabKey={platformTimeKey}
        onTabChange={handlePlatformTimeChange}
      >
        <div className="w-full h-[400px]">
          <Spin spinning={platformLoading}>
            <ReactECharts option={platformChartOption} style={{ height: 400, width: '100%' }} opts={{ renderer: 'canvas' }} />
          </Spin>
        </div>
      </SectionContainer>

      {/* 评价总体分析 */}
      <SectionContainer title={t('analysis.overallReviewAnalysis')} activeTabKey={reviewTimeKey} onTabChange={handleReviewTimeChange}>
        <div className="w-full h-[400px]">
          <Spin spinning={reviewLoading}>
            <ReactECharts option={reviewChartOption} style={{ height: 400, width: '100%' }} opts={{ renderer: 'canvas' }} />
          </Spin>
        </div>
      </SectionContainer>
    </>
  );
};
