import { WarningMessageSimpleDTO } from '@/types/warning';
import WarningIcon from '@/assets/svg/warning-clock.svg';
import { Badge, Button } from 'antd';
import { formatDateTimeString } from '@/utils/utils';
import { TextEllipsis } from '@/components/TextEllipsis';
import { WarningMessageTitle } from './WarningMessageTitle';
import { useTranslation } from 'react-i18next';

type IProps = {
  item: WarningMessageSimpleDTO;
  onClickDetail: (item: WarningMessageSimpleDTO) => void;
  isSelected?: boolean;
};

export const WarningMessageListItem = ({ item, onClickDetail, isSelected }: IProps) => {
  const { t } = useTranslation();
  const { read, warningType, reviewCreateTime, reviewComment, merchantName, analysisNotes } = item ?? {};

  return (
    <div
      className={`w-[457px] flex flex-col justify-start items-start p-5 gap-4 ${isSelected ? 'bg-[#F7F8FA]' : 'bg-white'}`}
      style={{
        color: 'rgba(0, 0, 0, 0.88)'
      }}
      onClick={() => onClickDetail(item)}
    >
      <div className="flex flex-row justify-between items-center gap-2 w-full">
        <div className="flex flex-row justify-start items-center flex-1 w-[calc(100%-148px)] gap-2">
          <img src={WarningIcon} alt="warning" className="w-5 h-5" />
          <WarningMessageTitle warningType={warningType} merchantName={merchantName} />
          {!read && <Badge dot />}
        </div>
        <div
          className="shrink-0 ml-2"
          style={{
            color: 'rgba(0, 0, 0, 0.25)'
          }}
        >
          {formatDateTimeString(reviewCreateTime)}
        </div>
      </div>
      <TextEllipsis
        className="flex flex-col justify-start items-start gap-2 w-full"
        text={t('warning.warningMessage', { analysisNotes, reviewComment })}
        maxWidth="100%"
        line={2}
      />
      <div className="flex flex-row justify-end items-center  w-full">
        <Button type="link" size="small">
          {t('common.viewAll')}
        </Button>
      </div>
    </div>
  );
};
