import { WarningMessageDetailDTO, WarningMessageSimpleDTO } from '@/types/warning';
import { WarningMessageTitle } from './WarningMessageTitle';
import { formatDateTimeString } from '@/utils/utils';
import { useCallback, useEffect, useState } from 'react';
import { getWarningDetail } from '@/api/warning';
import { WarningTag } from './WarningTag';
import { SingleReview } from '@/components/BusinessComponents/SingleReview';
import { useTranslation } from 'react-i18next';

export const WarningMessageDetail = ({ record }: { record: WarningMessageSimpleDTO }) => {
  const { t } = useTranslation();
  const [detail, setDetail] = useState<WarningMessageDetailDTO>({} as WarningMessageDetailDTO);
  const { warningId } = record;

  const getDetail = useCallback(async () => {
    const res = await getWarningDetail({ warningId });
    setDetail(res);
  }, [warningId]);

  useEffect(() => {
    if (warningId) {
      getDetail();
    }
  }, [warningId]);

  const {
    warningType,
    reviewCreateTime,
    merchantName,
    analysisNotes,
    reviewComment,
    reviewerName,
    commentEditTime,
    aiReply,
    replyContent,
    warningTypes,
    starRating,
    commentId
  } = detail || {};
  console.log(detail, 'detail', analysisNotes);

  return (
    <section className="p-5 flex flex-col gap-4 justify-start items-start bg-white flex-1 w-full">
      <div className="flex flex-row justify-between items-center gap-2 w-full">
        <div className="flex-1">
          <WarningMessageTitle warningType={warningType || ''} merchantName={merchantName || ''} />
        </div>
        <div className="shrink-0 text-[#999999]">{formatDateTimeString(reviewCreateTime || '')}</div>
      </div>
      <div>{`${t('warning.warningMessageOnly')}: ${analysisNotes}。`}</div>
      {warningTypes?.length && (
        <div className="flex flex-row justify-start items-center gap-2">
          {warningTypes.map(type => (
            <WarningTag key={type} title={type} />
          ))}
        </div>
      )}
      <div>
        {t('warning.negativeReviewContent')}: {reviewComment || ''}
      </div>
      <SingleReview
        key={`${warningId}-${commentId}`}
        review={{
          id: commentId,
          evaluateName: reviewerName,
          anonymousFlag: false,
          starRating,
          content: reviewComment,
          commentUpdateTime: reviewCreateTime || '',
          replyContent,
          replyUpdateTime: commentEditTime,
          aiTag: aiReply,
          merchantName
        }}
        showAvatar={false}
        refresh={() => {
          getDetail();
        }}
        replyStyle={{ marginTop: 16, paddingLeft: 16 }}
      />
    </section>
  );
};
