.warning-messages-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.warning-message-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.warning-icon {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  padding-top: 2px;
}

.warning-content {
  flex: 1;
  min-width: 0;
}

.warning-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 12px;
}

.warning-title {
  font-size: 15px;
  font-weight: 500;
  color: #262626;
  margin: 0;
  line-height: 1.4;
}

.warning-time {
  font-size: 13px;
  color: #8c8c8c;
  flex-shrink: 0;
  white-space: nowrap;
}

.warning-description {
  margin-bottom: 12px;
}

.warning-comment {
  font-size: 14px;
  color: #595959;
  line-height: 1.5;
}

.warning-actions {
  display: flex;
  justify-content: flex-end;
}

/* 详情页面样式 */
.warning-detail {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.warning-detail-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 20px;
  position: relative;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  border-radius: 4px;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  color: #fff;
}

.warning-detail-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  padding-left: 80px;
}

.warning-detail-time {
  font-size: 14px;
  opacity: 0.9;
  padding-left: 80px;
}

.warning-detail-content {
  padding: 24px;
}

.warning-detail-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.warning-detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.warning-detail-section h3 {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0 0 12px 0;
}

.warning-detail-section p {
  font-size: 14px;
  color: #595959;
  line-height: 1.6;
  margin: 0;
}

.reviewer-rating {
  margin: 8px 0;
}

.reviewer-rating span {
  font-size: 16px;
}

.review-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
  margin: 12px 0;
}

.edit-time {
  font-size: 12px;
  color: #8c8c8c;
  text-align: right;
}

.ai-reply-badge {
  display: inline-block;
  background: #52c41a;
  color: #fff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-bottom: 8px;
}

.reply-content {
  background: #f0f8ff;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #52c41a;
  margin: 12px 0;
}

.reply-actions {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .warning-messages-container {
    padding: 12px;
  }

  .warning-message-item {
    padding: 12px;
    margin-bottom: 8px;
  }

  .warning-header {
    flex-direction: column;
    gap: 6px;
  }

  .warning-time {
    align-self: flex-start;
  }

  .warning-detail-title {
    padding-left: 0;
    margin-top: 40px;
  }

  .warning-detail-time {
    padding-left: 0;
  }

  .warning-detail-content {
    padding: 16px;
  }
}

/* 主容器样式 - 确保占据整个可用空间 */
.warning-messages-container {
  height: 100%;
  display: flex;
  flex-direction: row;
  overflow: hidden; /* 防止整个容器滚动 */
}

/* 预警消息列表容器样式 - 确保只有这个区域可以滚动 */
.warning-messages-list {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px; /* 为滚动条留出空间 */
  /* 确保滚动只在这个容器内生效 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 详情区域样式 - 独立滚动 */
.warning-detail-container {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 加载指示器样式 */
.loading-indicator {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  background: white;
  flex-shrink: 0; /* 确保不会被压缩 */
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #8c8c8c;
  font-size: 14px;
}

.no-more-content {
  color: #bfbfbf;
  font-size: 14px;
}

/* 自定义滚动条样式 */
.warning-messages-list::-webkit-scrollbar,
.warning-detail-container::-webkit-scrollbar {
  width: 6px;
}

.warning-messages-list::-webkit-scrollbar-track,
.warning-detail-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.warning-messages-list::-webkit-scrollbar-thumb,
.warning-detail-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.warning-messages-list::-webkit-scrollbar-thumb:hover,
.warning-detail-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
