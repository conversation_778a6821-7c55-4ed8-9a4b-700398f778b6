import { useState, useEffect, useCallback, useRef } from 'react';
import { Spin, Empty } from 'antd';
import { getWarningList, markWarningAsRead } from '@/api/warning';
import type { WarningMessageSimpleDTO, GetWarningListParams } from '@/types/warning';
import './index.css';
import { useAuth } from '@/contexts/AuthContext';
import { WarningMessageListItem } from './WarningMessageListItem';
import { WarningMessageDetail } from './WarningMessageDetail';
import { useTranslation } from 'react-i18next';

export const WarningMessages = () => {
  const { agentId, merchantId } = useAuth();
  const { t } = useTranslation();
  const [warningList, setWarningList] = useState<WarningMessageSimpleDTO[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const [selectedWarning, setSelectedWarning] = useState<WarningMessageSimpleDTO | null>(null);

  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  const PAGE_SIZE = 10;

  // 获取预警列表
  const fetchWarningList = useCallback(
    async (pageNum: number, reset = false) => {
      if (loading) return;

      setLoading(true);
      try {
        const params: GetWarningListParams = {
          page: pageNum,
          size: PAGE_SIZE,
          agentId,
          merchantId
        };

        const response = await getWarningList(params);

        if (response) {
          const newWarnings = response.content || [];

          if (reset) {
            setWarningList(newWarnings);
          } else {
            setWarningList(prev => [...prev, ...newWarnings]);
          }

          setHasMore(!response.last && newWarnings.length === PAGE_SIZE);
          setPage(pageNum);
        }
      } finally {
        setLoading(false);
      }
    },
    [agentId, merchantId, t] // 移除 loading 依赖
  );

  // 初始化加载
  useEffect(() => {
    const initData = async () => {
      setWarningList([]);
      setPage(0);
      setHasMore(true);
      fetchWarningList(0, true);
    };
    initData();
  }, [agentId, merchantId, t, fetchWarningList]);

  // 设置无限滚动 - 使用 ref 来避免依赖问题
  useEffect(() => {
    if (!loadingRef.current) return;

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      const target = entries[0];
      if (target.isIntersecting && hasMore && !loading) {
        // 直接在这里处理加载更多，不依赖 fetchWarningList
        const loadMore = async () => {
          if (loading) return;

          setLoading(true);
          try {
            const params: GetWarningListParams = {
              page: page + 1,
              size: PAGE_SIZE,
              agentId,
              merchantId
            };

            const response = await getWarningList(params);

            if (response) {
              const newWarnings = response.content || [];
              setWarningList(prev => [...prev, ...newWarnings]);
              setHasMore(!response.last && newWarnings.length === PAGE_SIZE);
              setPage(page + 1);
            }
          } finally {
            setLoading(false);
          }
        };

        loadMore();
      }
    };

    observerRef.current = new IntersectionObserver(handleIntersection, { threshold: 1.0 });
    observerRef.current.observe(loadingRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, page, agentId, merchantId, t]); // 移除 fetchWarningList 依赖

  const handleMessageRead = useCallback((warningId: number) => {
    markWarningAsRead({ warningId });
    setWarningList(prev => prev.map(item => (item.warningId === warningId ? { ...item, read: true } : item)));
  }, []);

  // 处理查看详情
  const handleViewDetail = useCallback(
    (warning: WarningMessageSimpleDTO) => {
      setSelectedWarning(warning);
      handleMessageRead(warning.warningId);
    },
    [handleMessageRead]
  );

  return (
    <div className="flex flex-row justify-start gap-4 h-full warning-messages-container">
      {/* 左侧列表区域 - 独立滚动 */}
      <div className="w-[457px] flex flex-col h-full">
        {/* 列表容器 - 设置固定高度和滚动 */}
        <div className="flex-1 overflow-y-auto warning-messages-list">
          <div className="flex flex-col gap-4">
            {warningList.length === 0 && !loading ? (
              <Empty description={t('warning.noWarningMessages')} image={Empty.PRESENTED_IMAGE_SIMPLE} />
            ) : (
              warningList.map((warning, index) => (
                <WarningMessageListItem
                  key={`${warning.warningId}-${index}`}
                  item={warning}
                  onClickDetail={handleViewDetail}
                  isSelected={selectedWarning?.warningId === warning.warningId}
                />
              ))
            )}
          </div>
        </div>

        {/* 加载更多指示器 - 固定在底部 */}
        <div ref={loadingRef} className="loading-indicator flex-shrink-0">
          {loading && (
            <div className="loading-content">
              <Spin size="small" />
              <span>{t('common.loading')}</span>
            </div>
          )}

          {!hasMore && warningList.length > 0 && <div className="no-more-content">{t('warning.noMoreWarningMessages')}</div>}
        </div>
      </div>

      {/* 右侧详情区域 - 独立滚动 */}
      {selectedWarning && (
        <div className="flex-1 h-full overflow-y-auto warning-detail-container">
          <WarningMessageDetail record={selectedWarning} />
        </div>
      )}
    </div>
  );
};
