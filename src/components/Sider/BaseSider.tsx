import { Layout, Menu } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import { MenuCodeEnums, type MenuItemConfig } from '@/components/components';
import HomeSvg from '@/assets/svg/home.svg';
import SatisfactionSvg from '@/assets/svg/satisfaction.svg';
import AnalysisSvg from '@/assets/svg/analysis.svg';
import ReviewsSvg from '@/assets/svg/reviews.svg';
import MessagesSvg from '@/assets/svg/message.svg';
import SettingsSvg from '@/assets/svg/setting.svg';
import AuthSvg from '@/assets/svg/auth.svg';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';

const { Sider: AntdSider } = Layout;

interface MenuItemData {
  path: string;
  menuCode: MenuCodeEnums;
  icon: string;
  labelKey: string;
  requireAuth?: boolean;
}

interface BaseSiderProps {
  width?: number;
}

const getBaseMenuData = (): MenuItemData[] => [
  {
    path: '/',
    menuCode: MenuCodeEnums.Home,
    icon: HomeSvg,
    labelKey: 'navigation.home',
    requireAuth: false
  },
  {
    path: '/satisfaction',
    menuCode: MenuCodeEnums.Satisfaction,
    icon: SatisfactionSvg,
    labelKey: 'navigation.satisfaction',
    requireAuth: true
  },
  {
    path: '/analysis',
    menuCode: MenuCodeEnums.Analysis,
    icon: AnalysisSvg,
    labelKey: 'navigation.analysis',
    requireAuth: true
  },
  {
    path: '/reviews',
    menuCode: MenuCodeEnums.Reviews,
    icon: ReviewsSvg,
    labelKey: 'navigation.reviews',
    requireAuth: true
  },
  {
    path: '/messages',
    menuCode: MenuCodeEnums.Messages,
    icon: MessagesSvg,
    labelKey: 'navigation.messages',
    requireAuth: true
  },
  {
    path: '/settings',
    menuCode: MenuCodeEnums.Settings,
    icon: SettingsSvg,
    labelKey: 'navigation.settings',
    requireAuth: true
  },
  {
    path: '/auth',
    menuCode: MenuCodeEnums.Auth,
    icon: AuthSvg,
    labelKey: 'navigation.auth',
    requireAuth: true
  }
];

export const BaseSider = ({ width = 280 }: BaseSiderProps) => {
  const { menus } = useAuth();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const menuData = getBaseMenuData();

  const menuItems: MenuItemConfig[] = menuData.map(item => {
    const fullPath = item.path;

    return {
      ...item,
      key: fullPath,
      menuCode: item.menuCode,
      icon: null,
      label: (
        <div
          className="flex justify-start items-center gap-3"
          onClick={e => {
            e.preventDefault();
            navigate(fullPath, { replace: true });
          }}
        >
          <img className="menu-icon" src={item.icon} alt={item.path.slice(1) || 'icon'} />
          <span className="font-500 text-base w-full inline-block" style={{ cursor: 'pointer' }}>
            {t(item.labelKey)}
          </span>
        </div>
      )
    };
  });

  const filteredMenuItems = menuItems?.filter(item => {
    if (item.requireAuth) {
      return Array.isArray(menus) && menus?.some(menu => menu.menuCode === item.menuCode);
    }
    return true;
  });

  const getSelectedKey = () => {
    let path = location.pathname;
    // 统一去掉结尾的斜杠
    if (path !== '/' && path.endsWith('/')) {
      path = path.slice(0, -1);
    }
    // / 或 /index 匹配到 /
    if (path === '/' || path === '/index') {
      return '/';
    }
    return path;
  };

  return (
    <AntdSider width={width} style={{ background: '#fff' }}>
      <Menu
        mode="inline"
        selectedKeys={[getSelectedKey()]}
        className="custom-menu"
        items={filteredMenuItems?.map(item => ({
          key: item.key,
          icon: item.icon,
          label: item.label
        }))}
      />
    </AntdSider>
  );
};
