import React from 'react';
import ReactECharts from 'echarts-for-react';

interface CompassProps {
  value: number; // 0-100的百分比值
  title: string;
  platformName?: string; // 平台英文名
  totalReviews?: number; // 总评论数
  satisfiedReviews?: number; // 满意评论数
}

export const Compass: React.FC<CompassProps> = ({ value, title }) => {
  const option = {
    backgroundColor: 'transparent',
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'], // 调整中心位置
        radius: '90%',
        min: 0,
        max: 100,
        splitNumber: 10,
        startAngle: 225, // 从左下角开始
        endAngle: -45, // 到右下角结束，形成大半圈
        borderWidth: 2,

        // 仪表盘轴线
        axisLine: {
          lineStyle: {
            width: 3,
            color: [
              [0.4, '#973c29'], // 0-40% 红色
              [0.8, '#a96434'], // 40-80% 橙色
              [1, '#6ab448'] // 80-100% 绿色
            ]
          }
        },

        // 指针
        pointer: {
          itemStyle: {
            color: '#87CEEB' // 浅蓝色指针
          },
          width: 2,
          length: '75%',
          // 关键：设置指针只显示针头部分
          showAbove: true,
          offsetCenter: [0, 0]
        },

        // 指针中心圆点
        anchor: {
          show: true,
          showAbove: true,
          size: 6,
          itemStyle: {
            borderWidth: 3,
            borderColor: 'black', // 绿色边框
            color: '#6ab448', // 绿色填充
            shadowBlur: 4,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          }
        },
        // 刻度线
        axisTick: {
          show: false
        },
        // 刻度标签
        axisLabel: {
          show: false
        },
        // 分割线
        splitLine: {
          show: false
        },
        // 数值显示
        detail: {
          show: true,
          fontSize: 18,
          fontWeight: 'normal',
          color: '#333',
          fontFamily: 'DIN,Arial, sans-serif',
          offsetCenter: [0, '75%'],
          formatter: '{value}%'
        },

        data: [
          {
            value: value,
            name: ''
          }
        ]
      }
    ]
  };

  return (
    <div className="flex flex-col items-center justify-center h-[150px]">
      <ReactECharts option={option} style={{ width: '100%', height: '100%' }} opts={{ renderer: 'canvas' }} />
      <div className="text-base" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
        {title}
      </div>
    </div>
  );
};
