import React from 'react';
import ReactECharts from 'echarts-for-react';
import 'echarts-liquidfill';

interface LiquidFillChartProps {
  value: number; // 0-100的百分比值
  width?: number | string;
  height?: number | string;
  shape?: 'circle' | 'diamond' | 'triangle' | 'rect';
  showWave?: boolean;
}

// 根据百分比返回渐变色配置
const getSatisfactionGradient = (percentage: number) => {
  if (percentage > 80) {
    return ['#d7eef0', '#1597B4'];
  } else if (percentage >= 40) {
    return ['#ddc5ac', '#a96434'];
  } else {
    return ['#c88994', '#a83f4e'];
  }
};

export const LiquidFillChart: React.FC<LiquidFillChartProps> = ({
  value,
  width = '152px',
  height = '152px',
  shape = 'circle',
  showWave = false
}) => {
  // 确保value在0-100范围内
  const normalizedValue = Math.max(0, Math.min(100, value || 0));

  const option = {
    backgroundColor: '#FFFFFF',
    series: [
      {
        type: 'liquidFill',
        data: [
          {
            value: normalizedValue / 100,
            phase: 0
          },
          {
            value: normalizedValue / 100,
            phase: Math.PI
          }
        ],
        color: getSatisfactionGradient(normalizedValue),
        center: ['50%', '50%'],
        radius: '80%',
        shape: shape,
        outline: {
          show: true,
          borderDistance: 0,
          itemStyle: {
            borderColor: '#B3E5E8',
            borderWidth: 2,
            shadowBlur: 0,
            color: '#FFFFFF'
          }
        },
        label: {
          show: true,
          color: '#FFFFFF',
          fontSize: 32,
          fontWeight: 500,
          fontFamily: 'DIN, Arial, sans-serif',
          textVerticalAlign: 'middle',
          formatter: `${normalizedValue.toFixed(1)}%`,
          textBorderColor: 'rgba(217, 217, 217, 0.25)',
          textBorderWidth: 4
        },
        backgroundStyle: {
          color: '#FFFFFF'
        },
        // 波浪效果配置
        waveLength: '80%',
        waveHeight: showWave ? 8 : 0,
        waveAnimation: showWave,
        animationDuration: 2000,
        animationDurationUpdate: 1000,
        itemStyle: {
          shadowBlur: 0
        }
      }
    ]
  };

  return (
    <div
      style={{
        width,
        height,
        backgroundColor: '#FFFFFF',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden'
      }}
    >
      <ReactECharts
        option={option}
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: '#FFFFFF'
        }}
        opts={{ renderer: 'canvas' }}
      />
    </div>
  );
};
